{"name": "mockmate", "version": "1.0.13", "buildVersion": "********", "description": "<PERSON><PERSON><PERSON> is your AI-powered interview companion — helping you prepare, speak, and succeed with confidence. Whether you're tackling behavioral questions or technical deep dives, <PERSON><PERSON><PERSON> offers real-time voice transcription and intelligent responses, customized to your resume and role. It's like having a personal coach in your pocket.", "keywords": ["electron", "boilerplate", "react", "typescript", "ts", "sass", "webpack", "hot", "reload"], "homepage": "https://github.com/electron-react-boilerplate/electron-react-boilerplate#readme", "bugs": {"url": "https://github.com/electron-react-boilerplate/electron-react-boilerplate/issues"}, "repository": {"type": "git", "url": "git+https://github.com/electron-react-boilerplate/electron-react-boilerplate.git"}, "license": "MIT", "author": {"name": "Sirius", "email": "<EMAIL>", "url": "https://techcraft-solutions.web.app"}, "main": "./.erb/dll/main.bundle.dev.js", "scripts": {"build": "concurrently \"npm run build:main\" \"npm run build:renderer\"", "build:dll": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true NODE_OPTIONS=\"-r ts-node/register --no-warnings\" webpack --config ./.erb/configs/webpack.config.renderer.dev.dll.ts", "build:dll:prod": "cross-env NODE_ENV=production TS_NODE_TRANSPILE_ONLY=true NODE_OPTIONS=\"-r ts-node/register --no-warnings\" webpack --config ./.erb/configs/webpack.config.renderer.prod.dll.ts", "build:main": "cross-env NODE_ENV=production TS_NODE_TRANSPILE_ONLY=true NODE_OPTIONS=\"-r ts-node/register --no-warnings\" webpack --config ./.erb/configs/webpack.config.main.prod.ts", "build:renderer": "cross-env NODE_ENV=production TS_NODE_TRANSPILE_ONLY=true NODE_OPTIONS=\"-r ts-node/register --no-warnings\" webpack --config ./.erb/configs/webpack.config.renderer.prod.ts", "postinstall": "ts-node .erb/scripts/check-native-dep.js && electron-builder install-app-deps && npm run build:dll", "lint": "cross-env NODE_ENV=development eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "cross-env NODE_ENV=development eslint . --ext .js,.jsx,.ts,.tsx --fix", "package": "node .erb/scripts/sync-metadata.js && node .erb/scripts/prepare-package.js && rimraf dist && rimraf release/build && npm run build && electron-builder build --publish never && npm run build:dll:prod && node .erb/scripts/patch-latest-yml.js && node .erb/scripts/upload-to-server.js", "rebuild": "electron-rebuild --parallel --types prod,dev,optional --module-dir release/app", "prestart": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true NODE_OPTIONS=\"-r ts-node/register --no-warnings\" webpack --config ./.erb/configs/webpack.config.main.dev.ts", "start": "ts-node ./.erb/scripts/check-port-in-use.js && npm run prestart && npm run start:renderer", "start:main": "concurrently -k \"cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true webpack --watch --config ./.erb/configs/webpack.config.main.dev.ts\" \"electronmon .\"", "start:preload": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true NODE_OPTIONS=\"-r ts-node/register --no-warnings\" webpack --config ./.erb/configs/webpack.config.preload.dev.ts", "start:renderer": "cross-env NODE_ENV=development TS_NODE_TRANSPILE_ONLY=true NODE_OPTIONS=\"-r ts-node/register --no-warnings\" webpack serve --config ./.erb/configs/webpack.config.renderer.dev.ts", "test": "jest"}, "browserslist": ["extends browserslist-config-erb"], "prettier": {"singleQuote": true, "overrides": [{"files": [".prettier<PERSON>", ".eslintrc"], "options": {"parser": "json"}}]}, "jest": {"moduleDirectories": ["node_modules", "release/app/node_modules", "src"], "moduleFileExtensions": ["js", "jsx", "ts", "tsx", "json"], "moduleNameMapper": {"\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "<rootDir>/.erb/mocks/fileMock.js", "\\.(css|less|sass|scss)$": "identity-obj-proxy"}, "setupFiles": ["./.erb/scripts/check-build-exists.ts"], "testEnvironment": "jsdom", "testEnvironmentOptions": {"url": "http://localhost/"}, "testPathIgnorePatterns": ["release/app/dist", ".erb/dll"], "transform": {"\\.(ts|tsx|js|jsx)$": "ts-jest"}}, "dependencies": {"@electron/notarize": "^3.0.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.5", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@ts-stack/markdown": "^1.5.0", "dotenv": "^16.5.0", "electron-debug": "^4.1.0", "electron-log": "^5.3.2", "electron-updater": "^6.3.9", "form-data": "^4.0.2", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "openai": "^4.97.0", "pdf-parse": "^1.1.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-router-dom": "^7.5.2", "react-split": "^2.0.14", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "yaml": "^2.8.0"}, "devDependencies": {"@electron/rebuild": "^3.7.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.15", "@svgr/webpack": "^8.1.0", "@teamsupercell/typings-for-css-modules-loader": "^2.5.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/jest": "^29.5.14", "@types/node": "22.13.10", "@types/pdf-parse": "^1.1.5", "@types/react": "^19.0.11", "@types/react-dom": "^19.0.4", "@types/react-test-renderer": "^19.0.0", "@types/react-window": "^1.8.8", "@types/webpack-bundle-analyzer": "^4.7.0", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "browserslist-config-erb": "^0.0.3", "chalk": "^4.1.2", "concurrently": "^9.1.2", "copy-webpack-plugin": "^13.0.0", "core-js": "^3.41.0", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "css-minimizer-webpack-plugin": "^7.0.2", "detect-port": "^2.1.0", "electron": "^35.0.2", "electron-builder": "^25.1.8", "electron-devtools-installer": "^4.0.0", "electronmon": "^2.0.3", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-erb": "^4.1.0", "eslint-import-resolver-typescript": "^4.1.1", "eslint-import-resolver-webpack": "^0.13.10", "eslint-plugin-compat": "^6.0.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.3", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "mini-css-extract-plugin": "^2.9.2", "prettier": "^3.5.3", "react-refresh": "^0.16.0", "react-test-renderer": "^19.0.0", "rimraf": "^6.0.1", "sass": "^1.86.0", "sass-loader": "^16.0.5", "style-loader": "^4.0.0", "terser-webpack-plugin": "^5.3.14", "ts-jest": "^29.2.6", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths-webpack-plugin": "^4.2.0", "typescript": "^5.8.2", "url-loader": "^4.1.1", "webpack": "^5.98.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.0", "webpack-merge": "^6.0.1"}, "build": {"productName": "<PERSON><PERSON><PERSON>", "appId": "org.erb.Mockmate", "asar": true, "artifactName": "MockmateSetup-${version}.${ext}", "asarUnpack": "**\\*.{node,dll}", "files": ["dist", "node_modules", "package.json"], "afterSign": ".erb/scripts/notarize.js", "mac": {"target": {"target": "default", "arch": ["arm64", "x64"]}, "type": "distribution", "hardenedRuntime": true, "entitlements": "assets/entitlements.mac.plist", "entitlementsInherit": "assets/entitlements.mac.plist", "gatekeeperAssess": false}, "dmg": {"contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "win": {"target": ["nsis"], "icon": "assets/mockmate_icon_256.ico", "fileAssociations": [{"ext": "mm", "name": "Mockmate File"}], "publish": {"provider": "generic", "url": "http://*************:8109/updates/"}}, "linux": {"target": ["AppImage"], "category": "Development"}, "directories": {"app": "release/app", "buildResources": "assets", "output": "release/build"}, "extraResources": ["./assets/**", {"from": "tools/invisiwind/", "to": "invisiwind"}], "publish": {"provider": "generic", "url": "http://*************:8109/updates/", "channel": "latest"}, "extraMetadata": {"main": "./dist/main/main.js"}, "copyright": "Copyright © 2025 Sirius"}, "collective": {"url": "https://opencollective.com/electron-react-boilerplate-594"}, "devEngines": {"runtime": {"name": "node", "version": ">=14.x", "onFail": "error"}, "packageManager": {"name": "npm", "version": ">=7.x", "onFail": "error"}}, "electronmon": {"patterns": ["!**/**", "src/main/**", ".erb/dll/**"], "logLevel": "quiet"}}