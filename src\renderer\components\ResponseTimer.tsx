import React, { useState, useEffect, useRef } from 'react';
import { Typography } from '@mui/material';

interface ResponseTimerProps {
  isLoading: boolean;
  streamingContent: string;
}

const ResponseTimer: React.FC<ResponseTimerProps> = ({ isLoading, streamingContent }) => {
  const [responseTime, setResponseTime] = useState<number | null>(null);
  const startTimeRef = useRef<number | null>(null);
  
  useEffect(() => {
    if (isLoading && !streamingContent && !startTimeRef.current) {
      startTimeRef.current = Date.now();
      setResponseTime(null);
    }

    if (streamingContent && startTimeRef.current && responseTime === null) {
      const elapsed = Date.now() - startTimeRef.current;
      setResponseTime(elapsed);
    }

    if (!isLoading) {
      startTimeRef.current = null;
    }
  }, [isLoading, streamingContent, responseTime]);
  
  if (responseTime === null) {
    return null;
  }

  return (
    <Typography 
      variant="body2" 
      color="primary.light" 
      component="span"
      sx={{ ml: 1, fontFamily: 'monospace' }}
    >
      {responseTime.toLocaleString()}ms
    </Typography>
  );
};

export default ResponseTimer;