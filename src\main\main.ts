import path from 'path';
import { app, BrowserWindow, shell, webContents, ipcMain, desktopCapturer, systemPreferences, Tray, Menu, dialog } from 'electron';
import { autoUpdater } from 'electron-updater';
import log from 'electron-log';
import MenuBuilder from './menu';
import { resolveHtmlPath } from './util';
import PdfParse from 'pdf-parse';
import OpenAI from 'openai';
import { ChatCompletionMessageParam } from 'openai/resources/index.mjs';
import { version } from '../../package.json';
import { spawn } from 'child_process';
import fs from 'fs';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();
// Initialize OpenAI client 
console.log(process.env.OPENAI_API_KEY, process.env.DEEPGRAM_API_KEY, process.env.SERVER_URL);
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || ''
});

const broadcastUpdateStatus = (data: any) => {
  for (const wc of webContents.getAllWebContents()) {
    wc.send('update-status', data);
  }
};

function applyStealthMode() {
  const processName = path.basename(process.execPath);
  const invisidir = app.isPackaged
    ? path.join(process.resourcesPath, 'invisiwind')
    : path.join(__dirname, '..', '..', 'tools', 'invisiwind');

  const invisiwindPath = path.join(invisidir, 'Invisiwind.exe');

  if (!fs.existsSync(invisiwindPath)) {
    console.error('[ERROR] Invisiwind.exe not found at:', invisiwindPath);
    return;
  }

  // Run Invisiwind with hwnd as argument
  const invisiwind = spawn(invisiwindPath, [], {
    cwd: invisidir,
    stdio: ['pipe', 'pipe', 'pipe'],
    detached: true,
    windowsHide: true,
  });

  invisiwind.stdin.write(`hide ${processName}\n`);
  invisiwind.stdin.end();

  invisiwind.stdout.on('data', (data) => {
    console.log('[Invisiwind]', data.toString());
  });

  invisiwind.stderr.on('data', (err) => {
    console.error('[Invisiwind ERROR]', err.toString());
  });

  invisiwind.on('exit', (code) => {
    console.log(`[Invisiwind] exited with code ${code}`);
  });
}


class AppUpdater {
  constructor() {
    autoUpdater.setFeedURL({
      provider: 'generic',
      url: process.env.UPDATE_SERVER_URL || 'http://*************:8109/updates'
    });

    log.transports.file.level = 'info';
    autoUpdater.logger = log;
    autoUpdater.forceDevUpdateConfig = true;

    autoUpdater.on('checking-for-update', () => {
      log.info('Checking for updates...');
      broadcastUpdateStatus({ status: 'checking' });
    });

    autoUpdater.on('update-available', (info) => {
      log.info('Update available:', info);
      broadcastUpdateStatus({ status: 'available', info });
    });

    autoUpdater.on('update-not-available', (info) => {
      log.info('No updates available:', info);
    });

    autoUpdater.on('error', (err) => {
      log.error('Error during update check:', err);
    });

    autoUpdater.on('download-progress', (progress) => {
      log.info(`Download speed: ${progress.bytesPerSecond}`);
      log.info(`Downloaded ${progress.percent.toFixed(2)}%`);
      broadcastUpdateStatus({
        status: 'downloading',
        progress: {
          percent: progress.percent,
          transferred: progress.transferred,
          total: progress.total,
          speed: progress.bytesPerSecond
        }
      });

    });

    autoUpdater.on('update-downloaded', (info) => {
      log.info('Update downloaded.');
      broadcastUpdateStatus({ status: 'update-downloaded', info }); // ✅ ADD THIS
    });

    // autoUpdater.checkForUpdatesAndNotify();
  }
}
console.log('MAIN ENTRY: Running', __filename);
let mainWindow: BrowserWindow | null = null;
let tray: Tray | null = null;

// Add this function to create the tray
const createTray = () => {
  const RESOURCES_PATH = app.isPackaged
    ? path.join(process.resourcesPath, 'assets')
    : path.join(__dirname, '../../assets');

  const getAssetPath = (...paths: string[]): string => {
    return path.join(RESOURCES_PATH, ...paths);
  };

  tray = new Tray(getAssetPath('mockmate_icon_256.ico'));

  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'Show App',
      click: () => {
        if (mainWindow) {
          mainWindow.show();
        }
      }
    },
    {
      label: 'Quit',
      click: () => {
        app.quit();
      }
    }
  ]);

  tray.setToolTip('Mockmate');
  tray.setContextMenu(contextMenu);

  // Double-click to show the app
  tray.on('double-click', () => {
    if (mainWindow) {
      mainWindow.show();
    }
  });
};

ipcMain.on('ipc-example', async (event, arg) => {
  const msgTemplate = (pingPong: string) => `IPC test: ${pingPong}`;
  console.log(msgTemplate(arg));
  event.reply('ipc-example', msgTemplate('pong'));
});

ipcMain.handle('get-screen-sources', async () => {
  const sources = await desktopCapturer.getSources({
    types: ['screen', 'window'],
    thumbnailSize: { width: 320, height: 240 },
  });
  return sources.map((source) => ({
    id: source.id,
    name: source.name,
    thumbnail: source.thumbnail.toDataURL(),
  }));
});

ipcMain.handle('request-screen-permission', async () => {
  if (process.platform === 'darwin') {
    return await systemPreferences.getMediaAccessStatus('screen');
  }
  return 'granted';
});

ipcMain.handle('extract-pdf-text', async (_event, url: string) => {
  const response = await fetch(url);
  const arrayBuffer = await response.arrayBuffer();

  const buffer = Buffer.from(arrayBuffer);
  const text = await PdfParse(buffer).then(data => data.text);
  return text;
});

ipcMain.handle('get-summary', async (_event, transcriptions) => {
  try {
    const messages: ChatCompletionMessageParam[] = [
      {
        role: "system",
        content: `You are an AI assistant that summarizes hiring interviews in a structured and detailed format. Do not add fluff or filler. Output should be clearly organized into sections.`
      },
      {
        role: "user",
        content: `
    Please summarize the following interview transcript **for the candidate (me)** in a direct and helpful way. Use second-person voice (e.g., "you shared", "you answered", "your strengths", etc.). The goal is to help me reflect on how I did and what to expect next.

    Organize the summary using the following structure:

    ## Interview Recap with [Company Name]

    ### Interview Metadata
    - Interviewer name and role (if mentioned)
    - Your role being interviewed for
    - Format and purpose of the interview

    ### Topics Discussed
    - High-level technical or thematic areas covered in the conversation

    ### Interviewer Questions
    - List of questions you were asked

    ### Your Responses and Performance
    - Summarize what you shared and how well you addressed each question
    - Highlight areas where you demonstrated clarity, depth, or missed opportunities

    ### Key Strengths
    - Technical strengths you displayed
    - Soft skills or communication skills that stood out

    ### Questions You Asked
    - Summarize thoughtful questions you asked during the interview

    ### Closing Topics
    - Any wrap-up details, salary talk, timelines, or role clarifications

    ### Overall Assessment
    - Honest assessment of how you came across in the conversation
    - What went well, what could be improved, and how the interviewer likely perceived you

    ### Action Items / Next Steps
    - What you should prepare for next
    - Follow-ups to expect or complete

    Transcript:
    ${transcriptions}
    `
      }
    ];

    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages,
    });

    return response.choices[0]?.message?.content;
  } catch (error) {
    console.error('OpenAI API error:', error);
    throw error;
  }
})

// Add chat messages handler
ipcMain.handle('chat-completion', async (_event, messages) => {
  try {
    const controller = new AbortController();
    const signal = controller.signal;
    console.log(messages);
    const stream = await openai.chat.completions.create({
      model: "gpt-4o",
      messages,
      stream: true,
    }, { signal }); // Pass options which includes the signal

    // We can't directly stream to renderer, so we'll collect and return the full response
    let result = "";
    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content;
      if (content) {
        result += content;
        // Send incremental updates to renderer
        console.log('Sending chunk:', content);
        _event.sender.send('chat-chunk', content);
      }
    }
    return result;
  } catch (error) {
    console.error('OpenAI API error:', error);
    throw error;
  }
});

ipcMain.handle('generate-embedding', async (_event, text: string) => {
  try {
    const result = await openai.embeddings.create({
      input: text,
      model: 'text-embedding-ada-002',
    });
    return result.data[0].embedding;
  } catch (err) {
    console.error('Embedding API error:', err);
    throw err;
  }
});

ipcMain.handle('minimize-app', () => {
  if (mainWindow) {
    // Instead of minimizing, hide the window
    mainWindow.hide();
  }
});

ipcMain.handle('close-app', () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

ipcMain.handle('get-app-version', () => {
  return version;
});

ipcMain.handle('get-is-debug', () => {
  return isDebug;
});

// Add screenshot capture handler
ipcMain.handle('capture-screenshot', async (_event, sourceId) => {
  try {
    const sources = await desktopCapturer.getSources({
      types: ['window', 'screen'],
      thumbnailSize: { width: 1920, height: 1080 }
    });
    
    const source = sources.find(s => s.id === sourceId);
    if (!source) throw new Error('Source not found');
    
    // Return high-resolution screenshot
    return source.thumbnail.toDataURL();
  } catch (error) {
    console.error('Screenshot error:', error);
    throw error;
  }
});

ipcMain.handle('check-for-updates', async () => {
  try {
    const result = await autoUpdater.checkForUpdates();
    return { success: true, updateInfo: result?.updateInfo };
  } catch (error: any) {
    log.error('Manual update check failed:', error);
    return { success: false, error: error.message };
  }
});

ipcMain.handle('download-update', async () => {
  try {
    await autoUpdater.downloadUpdate(); // 👈 manually starts download
    return { success: true };
  } catch (error: any) {
    return { success: false, error: error.message };
  }
});

ipcMain.on('install-update', () => {
  autoUpdater.quitAndInstall();
});

if (process.env.NODE_ENV === 'production') {
  const sourceMapSupport = require('source-map-support');
  sourceMapSupport.install();
}

const isDebug =
  process.env.NODE_ENV === 'development' || process.env.DEBUG_PROD === 'true';

if (isDebug) {
  require('electron-debug').default();
}

const installExtensions = async () => {
  const installer = require('electron-devtools-installer');
  const forceDownload = !!process.env.UPGRADE_EXTENSIONS;
  const extensions = ['REACT_DEVELOPER_TOOLS'];

  return installer
    .default(
      extensions.map((name) => installer[name]),
      forceDownload,
    )
    .catch(console.log);
};

const createWindow = async () => {
  if (isDebug) {
    await installExtensions();
  }

  const RESOURCES_PATH = app.isPackaged
    ? path.join(process.resourcesPath, 'assets')
    : path.join(__dirname, '../../assets');

  const getAssetPath = (...paths: string[]): string => {
    return path.join(RESOURCES_PATH, ...paths);
  };

  mainWindow = new BrowserWindow({
    show: false,
    width: 1024,
    height: 768,
    icon: getAssetPath('mockmate_icon_256.ico'),
    transparent: true,
    backgroundColor: '#00000000',
    alwaysOnTop: true,
    roundedCorners: true,
    resizable: true,
    frame: false,
    hasShadow: false,
    vibrancy: 'under-window',
    skipTaskbar: true,
    webPreferences: {
      preload: app.isPackaged
        ? path.join(__dirname, 'preload.js')
        : path.join(__dirname, '../../.erb/dll/preload.js'),
      webSecurity: false, // for dev only
      devTools: true
    },
  });
  // mainWindow.setContentProtection(true);
  mainWindow.setVisibleOnAllWorkspaces(true, { visibleOnFullScreen: true });
  mainWindow.loadURL(resolveHtmlPath('index.html'));
  mainWindow.on('ready-to-show', () => {
    if (!mainWindow) {
      throw new Error('"mainWindow" is not defined');
    }
    setTimeout(() => {
      if (mainWindow && !isDebug) {
        applyStealthMode();
      }
    }, 1000);
    if (process.env.START_MINIMIZED) {
      mainWindow.minimize();
    } else {
      mainWindow.show();
    }
  });

  mainWindow.webContents.on('before-input-event', (_event, input) => {
    const isDevShortcut =
      input.type === 'keyDown' &&
      (
        (input.key === 'F12') || 
        (input.key.toLowerCase() === 'i' && input.control && input.shift) || // Ctrl+Shift+I
        (input.key.toLowerCase() === 'i' && input.meta && input.alt)         // Cmd+Option+I (mac)
      );

    if (isDevShortcut) {
      mainWindow?.webContents.openDevTools({ mode: 'detach' });
    }
  });


  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Add this to handle minimize event
  mainWindow.on('minimize', () => {
    if (process.platform === 'darwin') {
      app.dock.hide(); // On macOS, hide from dock when minimized
    }
    // The app will stay in tray when minimized
  });

  // Create the tray
  createTray();

  const menuBuilder = new MenuBuilder(mainWindow);
  menuBuilder.buildMenu();

  // Open urls in the user's browser
  mainWindow.webContents.setWindowOpenHandler((edata) => {
    shell.openExternal(edata.url);
    return { action: 'deny' };
  });

  // Remove this if your app does not use auto updates
  // eslint-disable-next-line
  console.log('App is packaged:', app.isPackaged);
  const appUpdater = new AppUpdater();
};

/**
 * Add event listeners...
 */

app.on('window-all-closed', () => {
  // Respect the OSX convention of having the application in memory even
  // after all windows have been closed
  if (process.platform !== 'darwin') {
    app.quit();
  }
});
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  app.quit(); // Another instance is running, exit this one
} else {
  app.on('second-instance', () => {
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.focus();
    }
  });
  app
    .whenReady()
    .then(() => {
      createWindow();
      app.on('activate', () => {
        // On macOS it's common to re-create a window in the app when the
        // dock icon is clicked and there are no other windows open.
        if (mainWindow === null) createWindow();
      });
    })
    .catch(console.log);

    // Add this to clean up the tray when app is quitting
  app.on('before-quit', () => {
    console.log('Application is quitting...');

    // Destroy tray
    if (tray) {
      tray.destroy();
      tray = null;
    }

    // Force close window
    if (mainWindow) {
      console.log('Closing main window...');
      mainWindow.removeAllListeners();
      if (!mainWindow.isDestroyed()) {
        mainWindow.destroy(); // Use destroy instead of close for more forceful termination
      }
      mainWindow = null;
    }
  });

}



// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught exception:', error);

  // Show error dialog
  if (app.isReady()) {
    dialog.showErrorBox('Error', `An unexpected error occurred: ${error.message}`);
  }

  // Force quit the app
  app.exit(1);
});

// Ensure clean exit
process.on('exit', () => {
  console.log('Process is exiting...');
  if (app.isReady()) {
    app.quit();
  }
});

// Add a handler for SIGINT (Ctrl+C)
process.on('SIGINT', () => {
  console.log('Received SIGINT signal...');
  app.quit();
});














