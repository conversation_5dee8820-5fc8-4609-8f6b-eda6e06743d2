import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { config } from "../config";
import {
  Box,
  Typography,
  IconButton,
  ToggleButtonGroup,
  ToggleButton
} from "@mui/material";
import RefreshIcon from '@mui/icons-material/Refresh';
import TableChartIcon from '@mui/icons-material/TableChart';
import ViewModuleIcon from '@mui/icons-material/ViewModule';

import { Interview } from "../config/types";
import InterviewTable from "../components/InterviewTable";
import { DATA_VIEW_MODE, LOCAL_STORAGE_KEYS } from "../config/constant";
import InterviewCardView from "../components/InterviewCards";
import { useLocalStorage } from "../hooks/useLocalStorage";

const Dashboard = () => {
  const [interviews, setInterviews] = useState<Interview[]>([]);
  const { value: viewMode, setValue: setViewMode } = useLocalStorage<string | null>(LOCAL_STORAGE_KEYS.VIEW_MODE, 'table');
  const { setValue: setCurrentInterview } = useLocalStorage<Interview | null>(LOCAL_STORAGE_KEYS.CURRENT_INTERVIEW, null);
  const { value: userToken } = useLocalStorage<string | null>(LOCAL_STORAGE_KEYS.AUTH_TOKEN, '');
  const navigate = useNavigate();
  
  const fetchInterviews = async () => {
    try {
      const response = await fetch(`${config.server_url}/jobs/interview_today?apiKey=${userToken}`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      if (data.data) {
        setInterviews(data.data);
      }
    } catch (error) {
      console.error("Error fetching interviews:", error);
    }
  };

  useEffect(() => {
    fetchInterviews();
  }, []);

  const handleViewMode = (
    event: React.MouseEvent<HTMLElement>,
    newMode: string | null,
  ) => {
    setViewMode(newMode);
  };


  const handleStartInterview = (interview: Interview) => {
    // You can store the interviewId in localStorage if needed for the interview page
    setCurrentInterview(interview);
    navigate('/interview');
  };

  return (
    <Box sx={{
      p: 3,
      backdropFilter: 'blur(12px)',
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.4)',
      borderRadius: 2,
      m: 2,
      height: 'calc(100% - 150px)'
    }}>

      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Typography variant="h6" sx={{ mt: 0, mb: 2 }}>
          Upcoming Interviews
          <IconButton onClick={fetchInterviews}>
            <RefreshIcon sx={{ color: '#90caf9' }} />
          </IconButton>
        </Typography>
        <ToggleButtonGroup
          value={viewMode}
          exclusive
          onChange={handleViewMode}
          aria-label="View Mode"
          size="small"
        >
          <ToggleButton value={DATA_VIEW_MODE.TABLE} aria-label="Table view">
            <TableChartIcon />
          </ToggleButton>
          <ToggleButton value={DATA_VIEW_MODE.CARD} aria-label="Card view">
            <ViewModuleIcon />
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>
      {viewMode === DATA_VIEW_MODE.TABLE ? (
        <InterviewTable interviews={interviews} onRowClick={handleStartInterview} />
      ) : (
        <InterviewCardView interviews={interviews} onRowClick={handleStartInterview} />
      )}

    </Box>
  );
};

export default Dashboard;
