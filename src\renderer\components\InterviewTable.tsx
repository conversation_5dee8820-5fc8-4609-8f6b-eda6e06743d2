import { type FC } from "react";
import Paper from "@mui/material/Paper";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Chip from "@mui/material/Chip";
import { STEP_TYPE } from "../config/constant";
import { Interview } from "../config/types";

interface InterviewTableProps {
    interviews: Interview[];
    onRowClick: (interview: Interview) => void;
}

const InterviewTable: FC<InterviewTableProps> = ({ interviews, onRowClick }) => {
    const getStepTypeLabel = (stepType: number) => {
        const color = stepType === 0 ? "primary" :
            stepType === 1 ? "secondary" :
                stepType === 2 ? "success" : "default";
        return <Chip label={STEP_TYPE[stepType]} color={color} size="small" />
    };

    // Find the next pending step (status: 0) for each interview
  const upcomingSteps = interviews
    .map(interview => {
      const pendingStep = interview.steps.find(step => step.status === 0);
      if (pendingStep) {
        return {
          _id: interview._id,
          interviewId: interview._id,
          projectName: interview.projectName,
          companyName: interview.projectName,
          jobRole: interview.jobRole,
          accountId: interview.accountId,
          jobDescription: interview.jobDescription,
          step: pendingStep,
          steps: []
        };
      }
      return null;
    })
    .filter(item => item !== null);

    return (
        <TableContainer component={Paper} sx={{
            height: 450,
            overflowY: 'auto',
            backgroundColor: 'rgba(255, 255, 255, 0.05)',
            backdropFilter: 'blur(8px)',
            border: '1px solid rgba(255, 255, 255, 0.08)',
            borderRadius: 2,
        }}>
            <Table>
                <TableHead>
                    <TableRow>
                        <TableCell align="left">Company</TableCell>
                        <TableCell align="center">Position</TableCell>
                        <TableCell align="center">Stage</TableCell>
                        <TableCell align="center">Interview Type</TableCell>
                        <TableCell align="right">Applied As</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {upcomingSteps.map((item) => item && (
                        <TableRow key={item.interviewId} sx={{
                            cursor: 'pointer',
                            '&:hover': {
                                backgroundColor: 'rgba(255, 255, 255, 0.2) !important',
                            },
                            '&:nth-of-type(2n - 1)': {
                                backgroundColor: 'rgba(255, 255, 255, 0.08)',
                            }
                        }}
                            onDoubleClick={() => onRowClick(item)}
                        >
                            <TableCell align="left" sx={{ color: '#ffffff', fontSize: '14px', fontWeight: 'bold' }}>{item.projectName}</TableCell>
                            <TableCell align="center">{item.jobRole}</TableCell>
                            <TableCell align="center">{item.step.step}</TableCell>
                            <TableCell align="center">{getStepTypeLabel(item.step.stepType)}</TableCell>
                            <TableCell align="right" sx={{ opacity: 0.5 }}>{item.accountId}</TableCell>
                        </TableRow>
                    ))}
                    {upcomingSteps.length === 0 && (
                        <TableRow>
                            <TableCell colSpan={6} align="center">
                                No upcoming interviews found
                            </TableCell>
                        </TableRow>
                    )}
                </TableBody>
            </Table>
        </TableContainer>
    )
};

export default InterviewTable;