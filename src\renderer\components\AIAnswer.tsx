import { Box, IconButton, Typography } from "@mui/material";
import { type FC } from "react";        
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import { Marked } from "@ts-stack/markdown";
import { Message } from "../hooks/useChat";
import { copyText } from "../utils";
import ResponseTimer from "./ResponseTimer";

interface AIAnswerProps {
  streamingContent?: string;
  isLoading?: boolean;
  messages: Message[];
}

const AIAnswer: FC<AIAnswerProps> = ({ messages, streamingContent = '', isLoading = false }) => {
  // Get the last assistant message
  const lastAssistantMessage = messages
    .filter(msg => msg.role === 'assistant')
    .pop()?.content || '';
  // Display streaming content if loading, otherwise show last message
  const displayContent = isLoading ? streamingContent : lastAssistantMessage;
  const copyAnswerToClipboard = async () => {
    copyText(displayContent);
  }
  return (
    <Box
      borderRadius={2}
      padding={3}
      marginRight={1}
      overflow="auto"
      height="100%"
      sx={{
        borderRadius: 2,
        padding: 1,
        marginRight: 1,
        overflow: 'auto',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <Typography variant="h6" color="primary" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        Answer
        <ResponseTimer isLoading={isLoading} streamingContent={streamingContent} />
        <Box sx={{ flexGrow: 1 }} />
        <IconButton size="small" onClick={copyAnswerToClipboard}>
          <ContentCopyIcon fontSize={"small"} />
        </IconButton>
      </Typography>
      <Box className="no-drag"
        sx={{
          overflowY: 'auto',
          flex: 1,
          padding: 2,
          borderRadius: 1,
          backdropFilter: 'blur(10px)',
          backgroundColor: '#8989896B',  // semi-transparent dark
        }}
      >
        {displayContent &&
        <div
          style={{
            fontSize: '16px',
            fontFamily: '"Segoe UI", Roboto, "Helvetica Neue", sans-serif',
            fontWeight: 400,
            letterSpacing: '0.2px',
            lineHeight: 1.6,
            color: '#ffffff',
            textShadow: '0 1px 3px rgba(0, 0, 0, 0.6)',
          }}
          dangerouslySetInnerHTML={{
            __html: Marked.parse(displayContent)
          }}
        />
        }
        {isLoading && !streamingContent && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Thinking...
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export default AIAnswer;

