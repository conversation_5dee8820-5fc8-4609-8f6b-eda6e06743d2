import { useEffect, useRef, useState } from "react";
import Container from "@mui/material/Container";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import CircularProgress from "@mui/material/CircularProgress";
import FormControl from "@mui/material/FormControl";
import FormHelperText from "@mui/material/FormHelperText";
import InputLabel from "@mui/material/InputLabel";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import Stepper from "@mui/material/Stepper";
import Step from "@mui/material/Step";
import StepLabel from "@mui/material/StepLabel";
import Paper from "@mui/material/Paper";
import { Interview } from "../config/types";
import { config } from "../config";
import { stripHtmlTags } from "../utils";
import { useAudioDevices } from "../hooks/useAudioDevices";
import { useLocalStorage } from "../hooks/useLocalStorage";
import { LOCAL_STORAGE_KEYS } from "../config/constant";

const steps = ['Company Info', 'Job Description', 'Resume & Details', 'Device Selection'];

const SetupPage = (
    { isLoading, onConfirmed }: { isLoading: boolean, onConfirmed: (interview: Interview) => void }
) => {
    const [interview, setInterview] = useState<Interview | undefined>();
    const [activeStep, setActiveStep] = useState(0);
    const { value: userToken } = useLocalStorage<string | null>(LOCAL_STORAGE_KEYS.AUTH_TOKEN, '');
    const { value: currentInterview } = useLocalStorage<Interview | null>(LOCAL_STORAGE_KEYS.CURRENT_INTERVIEW, null);
    // Device selection state
    const { micDevices } = useAudioDevices();
    const [selectedMic, setSelectedMic] = useState<string>("");
    const [screenSources, setScreenSources] = useState<any[]>([]);
    const [selectedSource, setSelectedSource] = useState<string>("");

    useEffect(() => {
        const loadInterviewAndUserInfo = async () => {
            if (!currentInterview) return;
            const strippedDescription = stripHtmlTags(currentInterview.jobDescription);
            const interviewWithStrippedDesc = { ...currentInterview, jobDescription: strippedDescription };

            // Then fetch user info and update with CV
            try {
                const response = await fetch(
                    `${config.server_url}/auth/get-info?apiKey=${userToken}`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                        },
                    }
                );
                const result = await response.json();
                const cv = result.data.accounts.find(
                    (account: any) => account.email === interviewWithStrippedDesc.accountId
                )?.cv as string;

                if (cv) {
                    const cvInStr = await window.electron.extractPdfText(`${config.server_url}/../../${cv}`);
                    setInterview({ ...interviewWithStrippedDesc, cvInStr });
                }
            } catch (error) {
                console.error("Failed to fetch user info or extract PDF:", error);
            }
        };

        if (currentInterview && userToken)
            loadInterviewAndUserInfo();
    }, [currentInterview, userToken]);

    // Load screen sources
    useEffect(() => {
        window.electron.getScreenSources().then((sources) => {
            setScreenSources(sources);
            // Auto-select first source if available
            if (sources.length > 0 && !selectedSource) {
                setSelectedSource(sources[0].id);
            }
        });
    }, []);

    // Auto-select first microphone if available
    useEffect(() => {
        if (micDevices.length > 0 && !selectedMic) {
            setSelectedMic(micDevices[0].deviceId);
        }
    }, [micDevices]);

    const handleNext = () => {
        setActiveStep((prevStep) => prevStep + 1);
    };

    const handleBack = () => {
        setActiveStep((prevStep) => prevStep - 1);
    };

    const isNextDisabled = () => {
        if (activeStep === 0) {
            return !interview?.companyName || !interview?.jobRole;
        }
        if (activeStep === 1) {
            return !interview?.jobDescription;
        }
        if (activeStep === 3) {
            return !selectedMic || !selectedSource;
        }
        return false;
    };

    const handleConfirm = () => {
        if (interview) {
            // Add device selection to interview object
            const interviewWithDevices = {
                ...interview,
                selectedMic,
                selectedSource
            };
            onConfirmed(interviewWithDevices);
        }
    };

    const renderStepContent = () => {
        switch (activeStep) {
            case 0:
                return (
                    <>
                        <Box>
                            <Typography color="primary">Company Name</Typography>
                            <TextField
                                fullWidth
                                variant="outlined"
                                value={interview?.companyName ?? ""}
                                onChange={(e) => setInterview((prev) => prev ? { ...prev, companyName: e.target.value } : undefined)}
                            />
                        </Box>

                        <Box>
                            <Typography color="primary">Role</Typography>
                            <TextField
                                fullWidth
                                variant="outlined"
                                value={interview?.jobRole ?? ""}
                                onChange={(e) => setInterview((prev) => prev ? { ...prev, jobRole: e.target.value } : undefined)}
                            />
                        </Box>
                    </>
                );
            case 1:
                return (
                    <Box>
                        <Typography color="primary">Job Description</Typography>
                        <TextField
                            fullWidth
                            variant="outlined"
                            multiline
                            value={interview?.jobDescription ?? ""}
                            onChange={(e) => setInterview((prev) => prev ? { ...prev, jobDescription: e.target.value } : undefined)}
                            rows={10}
                        />
                    </Box>
                );
            case 2:
                return (
                    <>
                        <Box>
                            <Typography color="primary">Resume</Typography>
                            <TextField
                                fullWidth
                                variant="outlined"
                                multiline
                                rows={6}
                                value={interview?.cvInStr ?? ""}
                                onChange={(e) => setInterview((prev) => prev ? { ...prev, cvInStr: e.target.value } : undefined)}
                            />
                        </Box>

                        <Box>
                            <Typography color="primary">Additional Information</Typography>
                            <TextField
                                fullWidth
                                variant="outlined"
                                multiline
                                value={interview?.additionalPrompt ?? ""}
                                onChange={(e) => setInterview((prev) => prev ? { ...prev, additionalPrompt: e.target.value } : undefined)}
                                rows={3}
                            />
                        </Box>

                        <FormControl fullWidth margin="normal">
                            <InputLabel>Interview Type</InputLabel>
                            <Select
                                value={interview?.interviewType || 'spoken'}
                                onChange={(e) => setInterview(prev => prev ? { ...prev, interviewType: e.target.value as any } : undefined)}
                                label="Interview Type"
                            >
                                <MenuItem value="spoken">Spoken Interview (Q&A, Behavioral)</MenuItem>
                                <MenuItem value="coding">Live Coding Interview (Beta)</MenuItem>
                                <MenuItem value="system_design">System Design Interview (Beta)</MenuItem>
                            </Select>
                            <FormHelperText>
                                Select the type of interview you're preparing for
                            </FormHelperText>
                        </FormControl>
                    </>
                );
            case 3:
                return (
                    <>
                        <Box>
                            <Typography color="primary" variant="h6" textAlign="center" gutterBottom>
                                Select Input Device
                            </Typography>
                            <FormControl fullWidth margin="normal">
                                <InputLabel>Microphone</InputLabel>
                                <Select
                                    value={selectedMic}
                                    onChange={(e) => setSelectedMic(e.target.value)}
                                    label="Microphone"
                                    MenuProps={{
                                        PaperProps: {
                                            sx: {
                                                backgroundColor: 'rgba(0, 0, 0)',
                                                color: 'white'
                                            }
                                        }
                                    }}
                                >
                                    {micDevices.map((d) => (
                                        <MenuItem key={d.deviceId} value={d.deviceId}>
                                            {d.label || `Mic ${d.deviceId}`}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Box>

                        <Box mt={3}>
                            <Typography color="primary" variant="h6" textAlign="center" gutterBottom>
                                Select Window to Record
                            </Typography>
                            <FormControl fullWidth margin="normal">
                                <InputLabel>Window</InputLabel>
                                <Select
                                    value={selectedSource}
                                    onChange={(e) => setSelectedSource(e.target.value)}
                                    label="Window"
                                    MenuProps={{
                                        PaperProps: {
                                            sx: {
                                                backgroundColor: 'rgba(0, 0, 0)',
                                                color: 'white'
                                            }
                                        }
                                    }}
                                >
                                    {screenSources.map((d) => (
                                        <MenuItem key={d.id} value={d.id}>
                                            {d.name}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Box>

                        {selectedSource && (
                            <Box mt={2} display="flex" flexDirection="column" alignItems="center">
                                <Typography color="primary" gutterBottom>Preview</Typography>
                                <img
                                    src={screenSources.find((d) => d.id === selectedSource)?.thumbnail}
                                    alt="Selected window"
                                    style={{ maxWidth: '100%', maxHeight: 200, objectFit: 'contain' }}
                                />
                            </Box>
                        )}
                    </>
                );
            default:
                return null;
        }
    };

    return (
        <Container maxWidth="sm" sx={{ display: "flex", flexDirection: "column", gap: 2, height: '89vh' }}>
            <Stepper activeStep={activeStep} sx={{ pt: 3, pb: 4 }}>
                {steps.map((label) => (
                    <Step key={label}>
                        <StepLabel>{label}</StepLabel>
                    </Step>
                ))}
            </Stepper>

            <Paper elevation={3} sx={{
                p: 3,
                flex: 1,
                overflowY: 'auto',
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
                backdropFilter: 'blur(12px)',
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.4)',
            }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                    {steps[activeStep]}
                </Typography>

                {renderStepContent()}
            </Paper>

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                <Button
                    variant="outlined"
                    onClick={handleBack}
                    disabled={activeStep === 0}
                >
                    Back
                </Button>

                {activeStep === steps.length - 1 ? (
                    <Button
                        variant="contained"
                        disabled={isLoading || !interview || !selectedMic || !selectedSource}
                        onClick={handleConfirm}
                    >
                        {isLoading ? <CircularProgress size={24} /> : "CONFIRM"}
                    </Button>
                ) : (
                    <Button
                        variant="contained"
                        onClick={handleNext}
                        disabled={isNextDisabled()}
                    >
                        Next
                    </Button>
                )}
            </Box>
        </Container>
    );
};

export default SetupPage;
