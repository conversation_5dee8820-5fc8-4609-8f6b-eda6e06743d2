# Mockmate

<div align="center">
  <img src="./assets/mockmate_icon_256.png" alt="Mockmate Logo" width="200" style="border-radius: 8px;"/>
</div>

**Mockmate** is an AI-powered interview copilot built with Electron and React(based on Electron-react-boilerplate). Unlike browser extensions, Mockmate runs as a desktop app and captures both microphone and system audio for a full interview experience. It provides real-time transcription, contextual AI feedback, and post-interview summaries.

## Features

* 🎙️ Records microphone and selected system window audio
* 🧠 Real-time transcription using Deepgram (with PCM stream + AudioWorklet)
* ✨ AI-generated interview feedback and summaries using OpenAI
* 💾 Uploads video recordings and transcripts to backend for storage
* 🔒 Local recovery support for unexpected interruptions
* 💻 Works across Windows and macOS

## Tech Stack

* Electron
* React + TypeScript
* Deepgram API
* OpenAI API
* Invisiwind (for Windows-specific stealth behavior)
* WebRTC, AudioWorklet

## Installation

```bash
git clone https://github.com/thereds11/mockmate.git
cd mockmate
npm install
```

## Running the App

```bash
npm start
```

This will start both the Electron app and the React frontend.

## Building for Production

```bash
npm run package
```

The compiled application will be in the `out/` directory.

## Development Notes

* Ensure Deepgram and OpenAI API keys are added to `config.ts`
* Use `.env` or `electron-store` for secure credential management
* Audio sources are selectable from the UI before recording starts

## License

This project is not open source and is intended for private use only by the author and their team. All rights reserved.
© 2025 Sirius

For major changes, please open an issue first to discuss what you'd like to change.
