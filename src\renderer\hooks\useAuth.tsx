import { createContext, useContext, useState, useEffect } from 'react';
import { useLocalStorage } from './useLocalStorage';
import { LOCAL_STORAGE_KEYS } from '../config/constant';

interface AuthContextType {
  username: string;
  token: string;
  setUsername: (name: string) => void;
  setToken: (token: string) => void;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const { value: storedUsername, setValue: setStoredUsername } = useLocalStorage(LOCAL_STORAGE_KEYS.USERNAME, '');
  const { value: storedToken, setValue: setStoredToken } = useLocalStorage(LOCAL_STORAGE_KEYS.AUTH_TOKEN, '');

  const [username, setUsername] = useState(storedUsername);
  const [token, setToken] = useState(storedToken);

  useEffect(() => {
    setStoredUsername(username);
  }, [username]);

  useEffect(() => {
    setStoredToken(token);
  }, [token]);

  return (
    <AuthContext.Provider value={{ username, token, setUsername, setToken }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const ctx = useContext(AuthContext);
  if (!ctx) throw new Error("useAuth must be used within AuthProvider");
  return ctx;
};
