import { <PERSON>, <PERSON><PERSON>, CircularP<PERSON>ress, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Linear<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Typo<PERSON> } from "@mui/material";
import { FC, useState, useCallback, useEffect, useRef } from "react";
import { <PERSON>uffer } from "buffer";
import { useNavigate } from "react-router-dom";
import Split from 'react-split';
import CameraAltIcon from '@mui/icons-material/CameraAlt';
import CloseIcon from '@mui/icons-material/Close';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import ExpandMoreIcon from '@mui/icons-material/ExpandMoreSharp';
import AIAnswer from "./AIAnswer";
import { TranscriptEntry, Transcription } from "./Transcription";
import useChat from "../hooks/useChat";
import { config } from "../config";
import { Interview } from "../config/types";
import { createRoot } from "react-dom/client";
import { useLocalStorage } from "../hooks/useLocalStorage";
import { LOCAL_STORAGE_KEYS } from "../config/constant";

interface InterviewContainerProps {
  selectedMic: string;
  selectedSource: string;
}

const InterviewContainer: FC<InterviewContainerProps> = ({ selectedMic, selectedSource }) => {
  const navigate = useNavigate();
  const [isRunning, setIsRunning] = useState(false);
  const [getFinalTranscript, setGetFinalTranscript] = useState<() => string>(() => () => '');
  const { messages, sendMessage, sendMessageWithScreenshot, isLoading, streamingContent } = useChat();
  const [getAllTranscripts, setGetAllTranscripts] = useState<() => TranscriptEntry[]>(() => () => []);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recordedChunksRef = useRef<Blob[]>([]);
  const [finalizeTranscripts, setFinalizeTranscripts] = useState<() => void>(() => () => { });

  const [isSaving, setIsSaving] = useState(false);
  const [finishStep, setFinishStep] = useState<number>(0);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [currentScreenshot, setCurrentScreenshot] = useState<string | null>(null);
  const [screenshotHistory, setScreenshotHistory] = useState<string[]>([]);
  const [interviewType, setInterviewType] = useState<'spoken' | 'coding' | 'system_design'>('spoken');
  const { value: currentInterview } = useLocalStorage<Interview | null>(LOCAL_STORAGE_KEYS.CURRENT_INTERVIEW, null);
  const { value: userToken } = useLocalStorage<string | null>(LOCAL_STORAGE_KEYS.AUTH_TOKEN, '');
  const [expanded, setExpanded] = useState(false);

  const shouldRender =
    screenshotHistory.length > 0 &&
    (interviewType === 'coding' || interviewType === 'system_design');


  // Load interview type from localStorage on mount
  useEffect(() => {
    if (currentInterview?.interviewType) {
      setInterviewType(currentInterview.interviewType);
    }
  }, [currentInterview]);

  // Add screenshot capture function
  const captureScreenshot = async () => {
    try {
      const screenshot = await window.electron.captureScreenshot(selectedSource);
      setCurrentScreenshot(screenshot);
      // Add to history when captured
      setScreenshotHistory(prev => [...prev, screenshot]);
    } catch (error) {
      console.error('Error capturing screenshot:', error);
    }
  };

  useEffect(() => {
    const onSpaceClicked = () => {
      handleAskClick();
    };
    window.addEventListener('keydown', (event) => {
      console.log(event.code);
      if (event.code === 'Space') {
        onSpaceClicked();
      }
    });
    return () => {
      window.removeEventListener('keydown', onSpaceClicked);
    };
  }, []);

  useEffect(() => {
    if (isRunning) {
      handleStartRecording();
    } else {
      mediaRecorderRef.current?.stop();
    }
  }, [isRunning]);

  const handleStartRecording = async () => {
    try {
      // 1. Capture screen/window video via sourceId
      const screenStream = await (navigator.mediaDevices as any).getUserMedia({
        video: {
          mandatory: {
            chromeMediaSource: 'desktop',
            chromeMediaSourceId: selectedSource,
          },
        },
        audio: {
          mandatory: {
            chromeMediaSource: 'desktop',
            chromeMediaSourceId: selectedSource,
          },
        },

      });
      console.log("screenStream");
      // 3. Capture microphone audio via selected input
      const micStream = await navigator.mediaDevices.getUserMedia({
        audio: { deviceId: selectedMic },
      });
      console.log("micStream");
      // 4. Combine all tracks
      const audioContext = new AudioContext();
      const destination = audioContext.createMediaStreamDestination();

      const systemSource = audioContext.createMediaStreamSource(screenStream);
      const micSource = audioContext.createMediaStreamSource(micStream);

      systemSource.connect(destination);
      micSource.connect(destination);

      const finalStream = new MediaStream([
        ...screenStream.getVideoTracks(),          // selected window/screen video
        ...destination.stream.getAudioTracks()
      ]);
      console.log('finalStream');
      // 5. Record
      const recorder = new MediaRecorder(finalStream, {
        mimeType: 'video/webm; codecs=vp9',
      });
      console.log("recorder");
      mediaRecorderRef.current = recorder;
      recordedChunksRef.current = [];

      recorder.ondataavailable = (e) => recordedChunksRef.current.push(e.data);
      recorder.start();
    } catch (err) {
      console.error("Recording error:", err);
    }
  };



  const handleAskClick = useCallback((isReact = false) => {
    if (!isRunning || isLoading) return; // Prevent multiple concurrent requests

    // Finalize all current transcriptions
    finalizeTranscripts();

    try {
      if (typeof getFinalTranscript !== 'function') {
        throw new Error('getFinalTranscript not ready');
      }

      const systemTranscript = getFinalTranscript();
      const allTranscripts = getAllTranscripts();

      // Improved context selection: get recent conversation segments
      const now = Date.now();
      const RECENT_TIME_WINDOW = 5 * 60 * 1000; // 5 minutes
      const MAX_CONTEXT_ENTRIES = 30;

      // Filter for recent transcripts and sort by timestamp
      const recentTranscripts = allTranscripts
        .filter(entry => {
          const entryTime = entry.timestamp || 0;
          return (now - entryTime) <= RECENT_TIME_WINDOW || !entry.timestamp; // Include entries without timestamp as fallback
        })
        .slice(-MAX_CONTEXT_ENTRIES); // Get the most recent entries

      // Build conversation history maintaining chronological order
      const history: { role: 'user' | 'system'; content: string }[] = [];

      for (const entry of recentTranscripts) {
        if (entry.transcription.trim()) {
          history.push({
            role: entry.user === 'mic' ? 'user' : 'system',
            content: entry.transcription
          });
        }
      }

      // ✅ Include the latest finalized system transcript if it's more recent
      if (systemTranscript.trim()) {
        // Check if this transcript is already in the history
        const isAlreadyIncluded = history.some(h =>
          h.role === 'system' && h.content.includes(systemTranscript.substring(0, 50))
        );

        if (!isAlreadyIncluded) {
          history.push({ role: 'system', content: systemTranscript });
        }
      }
      console.log("🧠 Contextual chat history", history);

      // For coding/system design interviews, include screenshot if available
      if ((interviewType === 'coding' || interviewType === 'system_design') && currentScreenshot) {
        // Send message with screenshot
        sendMessageWithScreenshot(
          isReact ? "React to it" : "",
          history,
          currentScreenshot,
          interviewType
        );
        // Reset current screenshot after sending
        setCurrentScreenshot(null);
      } else {
        // Regular message without screenshot
        sendMessage(isReact ? "React to it" : "", history);
      }
    } catch (error) {
      console.error("Error getting final transcript:", error);
    }
  }, [isRunning, getFinalTranscript, sendMessage, sendMessageWithScreenshot, finalizeTranscripts, currentScreenshot, interviewType]);

  const finishInterview = async () => {
    setIsSaving(true);
    setFinishStep(0); // Start at first step
    setIsRunning(false);
    try {
      // Step 1: Stop recording and prepare video (combined)
      const recorder = mediaRecorderRef.current;
      if (recorder && recorder.state !== 'inactive') {
        await new Promise(resolve => {
          recorder.onstop = resolve;
          recorder.stop();
        });
      }

      // Prepare video
      const videoBlob = new Blob(recordedChunksRef.current, { type: 'video/webm' });
      const arrayBuffer = await videoBlob.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      const file = new File([buffer], 'interview.webm', { type: 'video/webm' });
      const formData = new FormData();
      formData.append('video', file);

      // Step 2: Generate summary (show this as first visible step)
      setFinishStep(1);
      const allTranscriptions = getAllTranscripts();
      const transcriptions = allTranscriptions.map(t => `${t.user === "mic" ? "You: " : "Interviewer: "}${t.transcription}`).join('\n');
      const summary = await window.electron.getSummary(transcriptions);

      // Step 3: Upload video with progress tracking
      setFinishStep(2);

      // Create XMLHttpRequest for progress tracking
      const xhr = new XMLHttpRequest();
      const uploadPromise = new Promise<any>((resolve, reject) => {
        xhr.open('POST', `${config.server_url}/jobs/${currentInterview?._id}/steps/${currentInterview?.step?._id}/uploadVideo`);

        xhr.upload.onprogress = (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            setUploadProgress(progress);
          }
        };

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            resolve(JSON.parse(xhr.responseText));
          } else {
            reject(new Error(`HTTP Error: ${xhr.status}`));
          }
        };

        xhr.onerror = () => reject(new Error('Network Error'));
        xhr.send(formData);
      });

      const videoData = await uploadPromise;

      // Step 4: Save data (final step)
      setFinishStep(3);
      const response = await fetch(`${config.server_url}/jobs/saveRecapAndTranscription?apiKey=${userToken}`, {
        method: "POST",
        body: JSON.stringify({
          _id: currentInterview?._id,
          recap: summary,
          transcription: allTranscriptions,
          videoId: videoData.videoId
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      await response.json();

      // Complete
      setFinishStep(4);
      setTimeout(() => navigate('/'), 1000); // Give user a moment to see completion
    } catch (error) {
      console.error("Failed to save interview:", error);
    } finally {
      setIsSaving(false);
      setIsRunning(false);
    }
  }

  return (
    <Box
      height={"calc(100vh - 75px)"}
      display={"flex"}
      flexDirection={"column"}
    // sx={{ backgroundColor: '#f0f0f0' }}
    >
      {isSaving ? (
        <Box sx={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, backgroundColor: 'rgba(0, 0, 0, 0.7)', zIndex: 10000, display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
          <CircularProgress sx={{ mb: 3 }} />
          <Stepper activeStep={finishStep} sx={{ width: '80%', maxWidth: '600px', mb: 3 }}>
            <Step completed={finishStep > 0}>
              <StepLabel>Generate Summary</StepLabel>
            </Step>
            <Step completed={finishStep > 2}>
              <StepLabel>Upload Video</StepLabel>
            </Step>
            <Step completed={finishStep > 3}>
              <StepLabel>Save Interview</StepLabel>
            </Step>
          </Stepper>

          {finishStep === 2 && (
            <Box sx={{ width: '80%', maxWidth: '600px' }}>
              <Typography variant="body2" color="white" sx={{ mb: 1 }}>
                Uploading video: {uploadProgress}%
              </Typography>
              <LinearProgress variant="determinate" value={uploadProgress} sx={{ height: 10, borderRadius: 5 }} />
            </Box>
          )}

          <Typography variant="h6" color="white" sx={{ mt: 2 }}>
            {finishStep === 0 && "Preparing interview data..."}
            {finishStep === 1 && "Generating interview summary..."}
            {finishStep === 2 && "Uploading interview recording..."}
            {finishStep === 3 && "Saving interview data..."}
            {finishStep === 4 && "Interview saved successfully!"}
          </Typography>
        </Box>
      ) : ""}
      <Box
        display={"flex"}
        flex={1}
        padding={1}
        gap={2}
        overflow={"hidden"}
      >
        <Split
          className="split no-drag"
          sizes={[50, 50]} // default split
          minSize={200}   // minimum width in px for either pane
          expandToMin={true}
          gutterSize={8}
          gutterAlign="center"
          direction="horizontal"
          gutter={() => {
            const gutter = document.createElement('div');
            gutter.className = 'custom-gutter';

            const iconWrapper = document.createElement('div');
            iconWrapper.className = 'gutter-icon';
            gutter.appendChild(iconWrapper);

            // ✅ Mount MUI icon into this gutter
            const root = createRoot(iconWrapper);
            root.render(<DragIndicatorIcon fontSize="small" sx={{ color: '#aaa' }} />);

            return gutter;
          }}

          style={{ height: '100%', display: 'flex', width: '100%' }}
        >
          <div>
            <AIAnswer streamingContent={streamingContent} isLoading={isLoading} messages={messages} />
          </div>
          <div>
            <Transcription
              running={isRunning}
              selectedMic={selectedMic}
              onManualFinalize={(getterFn) => {
                setGetFinalTranscript(() => getterFn);
              }}
              onExposeTranscriptList={(getterFn) => setGetAllTranscripts(() => getterFn)}
              onFinalizeCurrentTranscripts={(callback) => setFinalizeTranscripts(() => callback)}
            />
          </div>
        </Split>
      </Box>
      {currentScreenshot && (
        <Box sx={{ mb: 2, position: 'relative' }}>
          <Typography variant="caption">
            Screenshot captured! Click "Get Answer" to analyze it.
          </Typography>
          <img
            src={currentScreenshot}
            alt="Current screenshot"
            style={{
              width: '100%',
              maxHeight: '200px',
              objectFit: 'contain',
              border: '1px solid #ccc',
              borderRadius: '4px'
            }}
          />
          <IconButton
            onClick={() => setCurrentScreenshot(null)}
            sx={{ position: 'absolute', top: 0, right: 0 }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      )}
      {shouldRender && <Box sx={{ mb: 2, mt: 2 }}>
        {/* <Button onClick={() => setExpanded(!expanded)} size="small">
          {expanded ? 'Hide Screenshots' : `Show Screenshots (${screenshotHistory.length})`}
        </Button> */}
        <IconButton size="small" sx={{ alignSelf: 'center' }}>
          <ExpandMoreIcon onClick={() => setExpanded(prev => !prev)} />
        </IconButton>
        <Collapse in={expanded}>
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2">Previous Screenshots ({screenshotHistory.length})</Typography>
            <Box sx={{ display: 'flex', gap: 1, overflowX: 'auto', pb: 1, mt: 1 }}>
              {screenshotHistory.map((screenshot, index) => (
                <Box
                  key={index}
                  sx={{
                    position: 'relative',
                    minWidth: '120px',
                    maxWidth: '120px',
                    cursor: 'pointer'
                  }}
                  onClick={() => setCurrentScreenshot(screenshot)}
                >
                  <img
                    src={screenshot}
                    alt={`Screenshot ${index + 1}`}
                    style={{
                      width: '100%',
                      height: '80px',
                      objectFit: 'cover',
                      border: '1px solid #ccc',
                      borderRadius: '4px'
                    }}
                  />
                  <Typography variant="caption" sx={{ display: 'block', textAlign: 'center' }}>
                    #{index + 1}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>
        </Collapse>
      </Box>}
      <Box
        display={"flex"}
        justifyContent={"space-between"}
        padding={2}
      >
        <Button
          variant="outlined"
          onClick={() => setIsRunning(!isRunning)}
          sx={{ minWidth: '100px' }}
        >
          {isRunning ? "Stop" : "Start"}
        </Button>
        <Button variant="contained" onClick={finishInterview} disabled={!isRunning}>
          Finish the interview
        </Button>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="contained"
            onClick={() => handleAskClick(true)}
            disabled={!isRunning}
            sx={{ minWidth: '150px' }}
          >
            {isLoading ? "Processing..." : "React(Beta)"}
          </Button>
          <Button
            variant="contained"
            onClick={() => handleAskClick()}
            disabled={!isRunning}
            sx={{ minWidth: '150px' }}
          >
            {isLoading ? "Processing..." : "Get Answer"}
          </Button>
          {(interviewType === 'coding' || interviewType === 'system_design') && (
            <Button
              variant="contained"
              color="secondary"
              onClick={captureScreenshot}
              disabled={!isRunning}
              startIcon={<CameraAltIcon />}
              sx={{ minWidth: '150px' }}
            >
              Capture Screen
            </Button>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default InterviewContainer;













