const fs = require('fs');
const path = require('path');
const YAML = require('yaml');

const ymlPath = path.resolve(__dirname, '../../release/build/latest.yml');
const PREFIX = 'http://192.168.13.99:8109/updates/';

if (!fs.existsSync(ymlPath)) {
  console.log(ymlPath);
  console.error('❌ latest.yml not found');
  process.exit(1);
}

const raw = fs.readFileSync(ymlPath, 'utf8');
const doc = YAML.parse(raw);

if (doc.files && Array.isArray(doc.files)) {
  doc.files = doc.files.map(file => ({
    ...file,
    url: PREFIX + file.url
  }));
}
if (doc.path) {
  doc.path = PREFIX + doc.path;
}

fs.writeFileSync(ymlPath, YAML.stringify(doc), 'utf8');
console.log('✅ latest.yml patched with full URLs');
