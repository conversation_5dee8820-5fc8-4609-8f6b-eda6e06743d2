import { useEffect, useState } from "react";

function shouldParseAsJSON(item: string): boolean {
  // Primitive types won't be stored with JSON format
  // If it looks like an object/array/null/boolean/number — parse it
  return (
    item.startsWith("{") ||
    item.startsWith("[") ||
    item === "null" ||
    item === "true" ||
    item === "false" ||
    (item !== "" &&
    !isNaN(Number(item))) // could still be a number
  );
}

export function useLocalStorage<T>(
  key: string,
  initialValue: T
): {
  value: T;
  setValue: React.Dispatch<React.SetStateAction<T>>;
} {
  const [value, setValue] = useState<T>(() => {
    const item = window.localStorage.getItem(key);
    if (item === null) return initialValue;
    try {
      if (shouldParseAsJSON(item)) {
        return JSON.parse(item);
      } else {
        return item as unknown as T;
      }
    } catch (e) {
      console.warn(`useLocalStorage: Failed to parse item for key "${key}"`);
      return initialValue;
    }
  });

  useEffect(() => {
    try {
      const serialized =
        value === null || typeof value === "object"
          ? JSON.stringify(value)
          : String(value);
      window.localStorage.setItem(key, serialized);
    } catch (e) {
      console.warn(`useLocalStorage: Failed to serialize value for key "${key}"`);
    }
  }, [key, value]);

  useEffect(() => {
    const handleStorageChange = () => {
      const item = window.localStorage.getItem(key);
      if (item === null) {
        setValue(initialValue);
      } else {
        try {
          if (shouldParseAsJSON(item)) {
            setValue(JSON.parse(item));
          } else {
            setValue(item as unknown as T);
          }
        } catch {
          setValue(initialValue);
        }
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, [key, initialValue]);


  return { value, setValue };
}
