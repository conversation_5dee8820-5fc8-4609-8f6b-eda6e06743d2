import React, { useEffect, useState, useCallback, useRef } from 'react';
import AutoSizer from 'react-virtualized-auto-sizer';
import EmojiEmotionsIcon from '@mui/icons-material/EmojiEmotions';
import { Box, Typography } from '@mui/material';
import { VariableSizeList as List } from 'react-window';
import { useDeepgramPCMStream } from '../hooks/useDeepgramPCMStream';
import { copyText } from '../utils';
import InterviewTimer from './InterviewTimer';

export interface TranscriptEntry {
  user: 'mic' | 'system';
  transcription: string;
  isFinal: boolean;
  timestamp?: number; // Optional timestamp for better tracking
}

interface TranscriptionProps {
  running: boolean;
  selectedMic: string;
  onManualFinalize?: (getTranscript: () => string) => void;
  onExposeTranscriptList?: (getTranscripts: () => TranscriptEntry[]) => void;
  onFinalizeCurrentTranscripts?: (callback: () => void) => void;
}

export const Transcription: React.FC<TranscriptionProps> = ({
  running,
  selectedMic,
  onManualFinalize,
  onExposeTranscriptList,
  onFinalizeCurrentTranscripts
}) => {
  const [transcripts, setTranscripts] = useState<TranscriptEntry[]>([]);
  const [isAutoScroll, setIsAutoScroll] = useState(true);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const transcriptsRef = useRef<TranscriptEntry[]>([]);
  const lastSystemTimestampRef = useRef<number | null>(null);
  const systemFinalizedRef = useRef<boolean>(true);
  const lastSystemIndexRef = useRef<number | null>(null);
  const lastFinalSystemTranscriptRef = useRef<string | null>(null);
  // Move the interimTranscriptRef to the component level
  const interimTranscriptRef = useRef<{ [key: string]: string }>({
    mic: '',
    system: ''
  });


  useEffect(() => {
    if (onExposeTranscriptList) {
      onExposeTranscriptList(() => transcriptsRef.current);
    }
  }, []); // Empty dependency array to run only once

  function transcriptOverlap(a: string, b: string | null): boolean {
    if (!a || !b) return false;
    const aw = a.toLowerCase().split(/\s+/);
    const bw = b.toLowerCase().split(/\s+/);
    const overlap = aw.filter(word => bw.includes(word));
    return overlap.length >= Math.min(3, aw.length); // heuristic
  }


  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Function to get the last system transcription and mark it as final
  const getLastSystemTranscription = useCallback(() => {
    const transcripts = transcriptsRef.current;
    const index = lastSystemIndexRef.current;

    // 🧠 Ensure we don't finalize too early (might still be speaking)
    const timeSinceLastSystem = Date.now() - (lastSystemTimestampRef.current ?? 0);
    const tooEarlyToFinalize = timeSinceLastSystem < 1000;

    if (
      index !== null &&
      transcripts[index] &&
      transcripts[index].user === 'system' &&
      !transcripts[index].isFinal
    ) {
      if (tooEarlyToFinalize) {
        console.warn("🟡 Skipping premature finalization (system might still be speaking)");
        // Instead of returning old cached transcript, return the current interim transcript
        return transcripts[index].transcription || '';
      }

      // ✅ Finalize and store it
      setTranscripts((prev) => {
        const updated = [...prev];
        updated[index] = {
          ...updated[index],
          isFinal: true,
          timestamp: Date.now()
        };
        transcriptsRef.current = updated;
        return updated;
      });

      const finalized = transcripts[index].transcription || '';
      lastFinalSystemTranscriptRef.current = finalized;

      systemFinalizedRef.current = true;
      lastSystemIndexRef.current = null;

      return finalized;
    }

    // Find the most recent system transcript instead of relying on cached value
    const recentSystemTranscripts = transcripts
      .filter(t => t.user === 'system')
      .slice(-3); // Get last 3 system transcripts

    if (recentSystemTranscripts.length > 0) {
      const mostRecent = recentSystemTranscripts[recentSystemTranscripts.length - 1];
      return mostRecent.transcription || '';
    }

    // Fallback to cached value only if no recent transcripts found
    return lastFinalSystemTranscriptRef.current || '';
  }, []);



  // Expose the function to parent component
  useEffect(() => {
    if (onManualFinalize) {
      onManualFinalize(() => getLastSystemTranscription());
    }
  }, []); // Empty dependency array to run only once

  // Add a function to finalize all current transcriptions
  const finalizeCurrentTranscripts = useCallback(() => {
    setTranscripts((prev) => {
      const updated = [...prev];
      // Mark all non-final transcriptions as final
      for (let i = 0; i < updated.length; i++) {
        if (!updated[i].isFinal) {
          updated[i] = {
            ...updated[i],
            isFinal: true
          };
        }
      }
      transcriptsRef.current = updated;
      return updated;
    });
  }, []);

  // Expose the finalize function to parent component
  useEffect(() => {
    if (onFinalizeCurrentTranscripts) {
      onFinalizeCurrentTranscripts(() => finalizeCurrentTranscripts());
    }
  }, []); // Empty dependency array to run only once

  const addTranscript = (entry: TranscriptEntry) => {
    if (!entry.transcription.trim()) return;

    const now = Date.now();

    // Track timestamp for system (for echo protection only)
    if (entry.user === 'system') {
      lastSystemTimestampRef.current = now;
    }

    // Echo filter for mic entries
    if (entry.user === 'mic') {
      const timeSinceLastSystem = now - (lastSystemTimestampRef.current ?? 0);
      const wordCount = entry.transcription.split(/\s+/).length;
      const isLikelyEcho =
        timeSinceLastSystem < 2000 &&
        (wordCount <= 5 || transcriptOverlap(entry.transcription, lastFinalSystemTranscriptRef.current));

      if (isLikelyEcho) {
        console.log('⏭️ Skipped likely echo from system:', entry.transcription);
        return;
      }
    }

    setTranscripts((prev) => {
      const updated = [...prev];

      // Find the last entry in the transcript list
      const lastEntry = updated.length > 0 ? updated[updated.length - 1] : null;

      // If the last entry is from a different user or all entries are finalized,
      // create a new entry instead of updating
      const shouldCreateNewEntry =
        !lastEntry ||
        lastEntry.user !== entry.user ||
        lastEntry.isFinal;

      if (shouldCreateNewEntry) {
        // Create a new entry
        updated.push({
          user: entry.user,
          transcription: entry.transcription,
          isFinal: entry.isFinal,
          timestamp: now // Add timestamp for better tracking
        });

        // Update references
        transcriptsRef.current = updated;
        if (entry.user === 'system') {
          lastSystemIndexRef.current = updated.length - 1;
          systemFinalizedRef.current = false;
        }

        // Update interim transcript
        interimTranscriptRef.current[entry.user] = entry.isFinal ? '' : entry.transcription;

        return updated;
      }

      // If we're here, we're updating the last entry (same user, not finalized)
      const lastIndex = updated.length - 1;

      if (entry.isFinal) {
        // Handle final transcription - mark as final and clear interim
        updated[lastIndex] = {
          ...lastEntry,
          transcription: entry.transcription,
          isFinal: true,
          timestamp: now
        };

        interimTranscriptRef.current[entry.user] = '';
      } else {
        // Handle interim transcription with improved overlap detection
        const existingText = lastEntry.transcription.toLowerCase();
        const incomingText = entry.transcription.toLowerCase();

        // Check if incoming text is a continuation or replacement
        const isReplacement = incomingText.length > existingText.length &&
                             incomingText.startsWith(existingText.substring(0, Math.min(existingText.length, 20)));

        const isDuplicate = existingText === incomingText;

        if (isDuplicate) {
          // Skip duplicate interim updates
          return prev;
        }

        if (isReplacement) {
          // Replace with the longer, more complete transcription
          updated[lastIndex] = {
            ...lastEntry,
            transcription: entry.transcription,
            isFinal: false,
            timestamp: now
          };
        } else {
          // Check for meaningful new content using better overlap detection
          const existingWords = existingText.split(/\s+/).filter(w => w.length > 0);
          const incomingWords = incomingText.split(/\s+/).filter(w => w.length > 0);

          // Find the longest common suffix to detect continuation
          let commonSuffixLength = 0;
          const minLength = Math.min(existingWords.length, incomingWords.length);

          for (let i = 1; i <= minLength; i++) {
            if (existingWords[existingWords.length - i] === incomingWords[incomingWords.length - i]) {
              commonSuffixLength = i;
            } else {
              break;
            }
          }

          // If there's significant new content, append it
          if (incomingWords.length > commonSuffixLength) {
            const newWords = incomingWords.slice(0, incomingWords.length - commonSuffixLength);
            if (newWords.length > 0 && newWords.join(' ').trim()) {
              updated[lastIndex] = {
                ...lastEntry,
                transcription: `${lastEntry.transcription} ${newWords.join(' ')}`.trim(),
                isFinal: false,
                timestamp: now
              };
            }
          }
        }

        interimTranscriptRef.current[entry.user] = entry.transcription;
      }

      // Update references
      transcriptsRef.current = updated;
      if (entry.user === 'system') {
        lastSystemIndexRef.current = lastIndex;
        systemFinalizedRef.current = entry.isFinal;
      }

      return updated;
    });
  };

  useEffect(() => {
    const fetchSystemSources = async () => {
      await window.electron.getScreenSources();
    };
    fetchSystemSources();
  }, []);

  // const micStream = useDeepgramStream('mic', addTranscript);
  const micStream = useDeepgramPCMStream('mic', addTranscript);
  const speakerStream = useDeepgramPCMStream('system', addTranscript);
  useEffect(() => {
    if (running === true) {
      start();
    } else {
      stop();
    }
  }, [running])
  const start = async () => {
    const micMedia = await navigator.mediaDevices.getUserMedia({
      audio: {
        deviceId: selectedMic ? { exact: selectedMic } : undefined,
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      }
    });
    await micStream.start(micMedia);
    try {
      const stream = await (navigator.mediaDevices as any).getUserMedia({
        audio: {
          mandatory: {
            chromeMediaSource: 'desktop',
            chromeMediaSourceId: 'screen:0:0',
          },
        },
        video: {
          mandatory: {
            chromeMediaSource: 'desktop',
            chromeMediaSourceId: 'screen:0:0',
          },
        },

      });
      const audioOnlyStream = new MediaStream(stream.getAudioTracks());
      await speakerStream.start(audioOnlyStream);
      // await speakerStream.start(speakerMedia);
    } catch (error) {
      console.error('❌ Failed to get system audio:', error);
    }
  };

  useEffect(() => {
    if (isAutoScroll && scrollContainerRef.current) {
      const el = scrollContainerRef.current;
      el.scrollTop = el.scrollHeight;
    }
  }, [transcripts, isAutoScroll]);


  const stop = () => {
    micStream.stop();
    speakerStream.stop();
  };

  return (
    <Box
      flex={1}
      height={"100%"}
      borderRadius={2}
      sx={{
        padding: 1,
        marginRight: 1,
        overflow: 'auto',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Typography variant="h6" color="primary">
          Transcription
        </Typography>
        <InterviewTimer isRunning={running} />
      </Box>
      <Box
        ref={scrollContainerRef}
        className="no-drag transcription-scroll-wrapper"
        sx={{
          padding: 1,
          borderRadius: 1,
          overflowY: 'auto',
          flex: 1,
        }}
        onScroll={(e) => {
          const target = e.target as HTMLDivElement;
          const atBottom = target.scrollHeight - target.scrollTop - target.clientHeight < 20;
          setIsAutoScroll(atBottom);
        }}

      >
        {transcripts.length === 0 ? (
          <Typography
            color="text.secondary"
            sx={{
              borderRadius: 3,
              p: 2,
              backdropFilter: 'blur(10px)',
              backgroundColor: 'rgba(0, 0, 0, 0.5)',  // semi-transparent dark
            }}
          >
            Transcriptions will appear here when you start recording...
          </Typography>
        ) : (
          transcripts.slice(-100).map((t, index) => {
            const isHovering = hoveredIndex === index;
            const handleCopyToClipboard = async () => {
              await copyText(t.transcription);
            }
            return (
              <Box
                key={index}
                sx={{
                  p: '8px 16px',
                  background:
                    t.user === 'mic'
                      ? 'rgba(102, 101, 101, 0.71)'
                      : 'rgba(102, 101, 101, 0.2)',
                  borderRadius: '16px',
                  marginLeft: t.user === 'mic' ? '30px' : 0,
                  mb: 1,
                  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.2)',
                  position: 'relative'
                }}
                onMouseEnter={() => setHoveredIndex(index)}
                onMouseLeave={() => setHoveredIndex(null)}
              >
                <Typography component="span" color='text.secondary'
                  sx={{
                    fontSize: '16px',
                    fontFamily: '"Segoe UI", Roboto, "Helvetica Neue", sans-serif',
                    fontWeight: 400,
                    letterSpacing: '0.2px',
                    lineHeight: 1.6,
                    color: '#ffffff',
                    textShadow: '0 1px 3px rgba(0, 0, 0, 0.6)',
                    display: 'flex',
                    gap: '6px',
                    alignItems: 'center',
                    pr: isHovering ? 2 : 0 // Add padding when hovering to prevent text overlap with copy icon
                  }}
                >
                  {t.user === 'system' && <EmojiEmotionsIcon fontSize="small" />}
                  <span>{t.transcription}</span>
                </Typography>

                {isHovering && (
                  <Box
                    onClick={handleCopyToClipboard}
                    sx={{
                      position: 'absolute',
                      right: '8px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      backgroundColor: 'rgba(0, 0, 0, 0.3)',
                      borderRadius: '50%',
                      width: '28px',
                      height: '28px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      '&:hover': {
                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                      }
                    }}
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                      <path d="M16 1H4C2.9 1 2 1.9 2 3V17H4V3H16V1ZM19 5H8C6.9 5 6 5.9 6 7V21C6 22.1 6.9 23 8 23H19C20.1 23 21 22.1 21 21V7C21 5.9 20.1 5 19 5ZM19 21H8V7H19V21Z" />
                    </svg>
                  </Box>
                )}
              </Box>
            )
          })
        )}
      </Box>
    </Box>
  );
};



















