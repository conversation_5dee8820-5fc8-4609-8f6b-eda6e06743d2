import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  LinearProgress,
  Snackbar,
  Typography,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Box
} from "@mui/material";
import UpgradeIcon from '@mui/icons-material/Upgrade';
import { keyframes } from "@emotion/react";

const pulse = keyframes`
  0% { transform: scale(1); opacity: 0.6; }
  50% { transform: scale(1.3); opacity: 1; }
  100% { transform: scale(1); opacity: 0.6; }
`;

const CheckUpdates = () => {
  const [status, setStatus] = useState<string | null>(null);
  const [progress, setProgress] = useState<number>(0);
  const [updateReady, setUpdateReady] = useState(false);
  const [showSnackbar, setShowSnackbar] = useState(false);
  const [showDialog, setShowDialog] = useState(false);

  // Handle update status events
  useEffect(() => {
    const unsubscribe = window.electron.autoUpdate.onUpdateStatus((data) => {
      setStatus(data.status);
      console.log("Update status:", data);

      if (data.status === "downloading" && data.progress) {
        setProgress(data.progress.percent);
        setShowSnackbar(true);
      }

      if (data.status === "available") {
        console.log('available');
        setShowSnackbar(true);
      }

      if (data.status === "update-downloaded") {
        setUpdateReady(true);
        setShowDialog(true);
      }
    });

    return () => {unsubscribe()};
  }, []);

  // Periodic and initial check
  useEffect(() => {
    window.electron.autoUpdate.checkForUpdates(); // initial

    const interval = setInterval(() => {
      window.electron.autoUpdate.checkForUpdates();
    }, 30 * 60 * 1000); // every 30 minutes

    return () => clearInterval(interval);
  }, []);

  const handleClickUpdateIcon = async () => {
    if (status === "available") {
      await window.electron.autoUpdate.downloadUpdate();
    }
  };

  const handleInstall = () => {
    setShowDialog(false);
    window.electron.autoUpdate.installUpdate();
  };

  const handleCloseSnackbar = () => setShowSnackbar(false);
  const handleCloseDialog = () => setShowDialog(false);

  const renderSnackbarMessage = () => {
    if (status === "available") return "A new update is available";
    if (status === "downloading") return `Downloading update... ${progress.toFixed(1)}%`;
    if (status === "update-downloaded") return "Update downloaded and ready";
    return "";
  };

  return (
    <>
      {/* 🔔 Animated Update Icon */}
      {status === "available" && (
        <Tooltip title="Update available">
          <IconButton
            onClick={handleClickUpdateIcon}
            size="small"
            sx={{ animation: `${pulse} 1.5s infinite ease-in-out`, color: "#90caf9", ml: 1 }}
          >
            <UpgradeIcon />
          </IconButton>
        </Tooltip>
      )}

      {/* 📦 Download progress bar inside snackbar */}
      {status === "downloading" && <Snackbar
        open={showSnackbar}
        autoHideDuration={status === "downloading" ? null : 6000}
        onClose={handleCloseSnackbar}
        message={renderSnackbarMessage()}
        anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
      >
        {
          <Box sx={{ width: "100%", px: 2, pb: 1 }}>
            <Typography variant="caption" color="inherit">
              {renderSnackbarMessage()}
            </Typography>
            <LinearProgress variant="determinate" value={progress} />
          </Box>
        }
      </Snackbar>}

      {/* ⚠️ Dialog asking user to restart */}
      <Dialog open={showDialog} onClose={handleCloseDialog}>
        <DialogTitle>Update Ready</DialogTitle>
        <DialogContent>
          <Typography>
            A new version has been downloaded. Would you like to restart the app to apply the
            update?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Later</Button>
          <Button onClick={handleInstall} color="primary" variant="contained">
            Restart Now
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default CheckUpdates;
