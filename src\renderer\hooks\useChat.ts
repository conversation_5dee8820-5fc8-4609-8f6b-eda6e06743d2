import { useState, useEffect, useCallback } from 'react';
import { cosineSimilarity, getEmbedding } from './useEmbedding';
import { config } from '../config';
import { useLocalStorage } from './useLocalStorage';
import { LOCAL_STORAGE_KEYS } from '../config/constant';

export type Message = {
  role: 'system' | 'user' | 'assistant';
  content: string | any; // Allow for complex content with images
};

export const useChat = () => {
  const { value: messages, setValue: setMessages } = useLocalStorage<Message[]>(LOCAL_STORAGE_KEYS.CHAT_HISTORY, []);
  const [isLoading, setIsLoading] = useState(false);
  const [streamingContent, setStreamingContent] = useState('');
  const { value: sessionEmbedding } = useLocalStorage<number[]>(LOCAL_STORAGE_KEYS.SESSION_EMBEDDING, []);
  // Setup streaming listener
  useEffect(() => {
    const removeListener = window.electron.chat.onChunk((chunk) => {
      setStreamingContent(prev => prev + chunk);
    });

    return () => {
      removeListener()
    };
  }, []);

  // New function to send messages with screenshots
  const sendMessageWithScreenshot = useCallback(
    async (
      content: string,
      contextMessages: Message[],
      screenshot: string,
      interviewType: 'coding' | 'system_design'
    ) => {
      setIsLoading(true);
      setStreamingContent('');

      try {
        // Get the existing system message from messages state
        const existingSystemMessage = messages[0];
        
        // Create system instruction based on interview type
        const interviewContextualizedSystemMessage = {
          role: 'system',
          content: `
            ${existingSystemMessage?.content || ''}
            
            ${interviewType === 'coding'
              ? `Now, you're doing a live coding interview. Analyze the code in the screenshot, offer insights, and respond naturally as the candidate.`
              : `Now, you're doing a system design interview. Use the screenshot or diagram as a reference and offer thoughtful design-level insights, still speaking as the candidate.`}
          `.trim()
        };

        
        // Prepare messages for API call - include ALL previous messages for context
        const apiMessages = [
          interviewContextualizedSystemMessage,
          ...messages.slice(1), // Include all previous messages except system
          ...contextMessages,    // Add new context messages
          { 
            role: 'user', 
            content: [
              { type: 'text', text: content || "Please analyze what's shown in this screenshot and continue our conversation." },
              { type: 'image_url', image_url: { url: screenshot } }
            ]
          }
        ];
        
        // Call OpenAI API directly
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${config.openai_api_key}`
          },
          body: JSON.stringify({
            model: 'gpt-4o',
            messages: apiMessages,
            stream: false
          })
        });
        
        const data = await response.json();
        const assistantContent = data.choices[0].message.content;
        
        // Create user and assistant messages for the chat history
        const userMessage: Message = {
          role: 'user',
          content: content || "Please analyze the screenshot I've shared."
        };
        
        const assistantMessage: Message = {
          role: 'assistant',
          content: assistantContent
        };
        
        // Update messages state
        setMessages(prev => [
          ...prev,
          userMessage,
          assistantMessage
        ]);
        
        return assistantContent;
      } catch (error) {
        console.error('Error sending message with screenshot:', error);
        throw error;
      } finally {
        setIsLoading(false);
        setStreamingContent('');
      }
    },
    [messages] // Add messages as dependency
  );

  const sendMessage = useCallback(
    async (
      content?: string, // optional now
      contextMessages?: Message[] // interviewer + user past dialogue
    ) => {
      setIsLoading(true);
      setStreamingContent('');
      const userMessage: Message | null = content?.trim()
        ? { role: 'user', content }
        : null;

      let finalMessages: Message[] = [];

      let dynamicInstruction = '';
      if (content) {
        const userEmbedding = await getEmbedding(content);
        const similarity = cosineSimilarity(userEmbedding, sessionEmbedding);
        dynamicInstruction =
          similarity > 0.7
            ? ''
            : 'If the question is general or small talk, you should still respond in character as the candidate in the resume.';
      }

      if (contextMessages && contextMessages.length > 0) {
        finalMessages = [
          {
            ...messages[0], // system prompt
            content: `${messages[0].content} ${dynamicInstruction}`,
          },
          ...contextMessages,
          ...(userMessage ? [userMessage] : []),
        ];
      } else {
        const updatedMessages = [...messages];
        if (userMessage) updatedMessages.push(userMessage);

        finalMessages = [
          {
            ...updatedMessages[0],
            content: `${updatedMessages[0].content} ${dynamicInstruction}`,
          },
          ...updatedMessages.slice(-10),
        ];
      }

      console.log('🧠 finalMessages to GPT:', finalMessages);

      try {
        const assistantContent = await window.electron.chat.sendMessage(finalMessages);

        const assistantMessage: Message = {
          role: 'assistant',
          content: assistantContent,
        };

        setMessages(prev => [
          ...prev,
          ...(userMessage ? [userMessage] : []),
          assistantMessage,
        ]);
      } catch (error) {
        if (!(error instanceof DOMException && error.name === 'AbortError')) {
          console.error('Failed to get response:', error);
        }
      } finally {
        setIsLoading(false);
        setStreamingContent('');
      }
    },
    [messages]
  );

  return {
    messages,
    sendMessage,
    sendMessageWithScreenshot, // Export the new function
    isLoading,
    streamingContent
  };
};

export default useChat;


