
export interface Step {
    step: string;
    status: number;
    stepType: number;
    date: string;
    video: string | null;
    createdAt: string;
    updatedAt: string;
    _id: string;
}
  
export interface Interview {
    _id: string;
    companyName: string;
    projectName: string;
    jobRole: string;
    jobDescription: string;
    accountId: string;
    step?: Step;
    steps: Step[];
    cvInStr?: string;
    additionalPrompt?: string;
    interviewType?: 'spoken' | 'coding' | 'system_design';
    selectedMic?: string;
    selectedSource?: string;
}
