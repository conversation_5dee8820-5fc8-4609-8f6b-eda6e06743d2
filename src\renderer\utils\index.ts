export const stripHtmlTags = (html: string): string => {
  if (!html) return '';
  
  // Use DOMParser instead of innerHTML for safer parsing
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');
  
  // Extract text content without executing any scripts
  return doc.body.textContent || '';
};

export const copyText = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text);
  } catch (err) {
    console.error('Failed to copy: ', err);
  }
}