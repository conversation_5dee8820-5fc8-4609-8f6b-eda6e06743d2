import { useRef, useCallback } from 'react';
import * as path from 'path';
import { config } from '../config';
import { extractKeywordsFromResume } from '../utils/keywords';
import { useLocalStorage } from './useLocalStorage';
import { Interview } from '../config/types';
import { LOCAL_STORAGE_KEYS } from '../config/constant';

interface TranscriptEntry {
  user: 'mic' | 'system';
  transcription: string;
  isFinal: boolean;
}

export const useDeepgramPCMStream = (
  label: 'mic' | 'system',
  onTranscript: (entry: TranscriptEntry) => void
) => {
  const { value: currentInterview } = useLocalStorage<Interview | null>(LOCAL_STORAGE_KEYS.CURRENT_INTERVIEW, null);
  const wsRef = useRef<WebSocket | null>(null);
  const audioCtxRef = useRef<AudioContext | null>(null);
  const sourceRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const workletNodeRef = useRef<AudioWorkletNode | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isReconnectingRef = useRef<boolean>(false);
  const reconnectAttemptsRef = useRef<number>(0);
  const MAX_RECONNECT_ATTEMPTS = 19;
  const RECONNECT_DELAY = 3000; // 3 seconds

  const setupWebSocket = useCallback((workletNode: AudioWorkletNode) => {
    const DEEPGRAM_API_KEY = config.deepgram_api_key;
    const resumeText = currentInterview?.cvInStr;
    const keywords = extractKeywordsFromResume(resumeText || "") ?? [];
    const keywordQuery = encodeURIComponent(keywords.join(','));
    
    const ws = new WebSocket(
      `wss://api.deepgram.com/v1/listen?model=nova-2&language=en-US&punctuate=true&smart_format=true&encoding=linear16&sample_rate=16000&vad_turnoff=300&interim_results=true&diarize=true&keywords=${keywordQuery}&keyword_threshold=0.5`,
      ['token', DEEPGRAM_API_KEY]
    );
    wsRef.current = ws;

    ws.onopen = () => {
      console.log(`${label} WebSocket connected`);
      isReconnectingRef.current = false;
      reconnectAttemptsRef.current = 0;
      
      // Setup worklet message handler
      workletNode.port.onmessage = (event) => {
        const pcmChunk = event.data;
        if (ws.readyState === WebSocket.OPEN) {
          ws.send(pcmChunk);
        } else {
          console.warn(`${label} WebSocket not open — triggering reconnect`);
          attemptReconnect();
        }
      };
    };

    ws.onclose = (event) => {
      console.log(`${label} WebSocket closed:`, event.code, event.reason);
      if (ws.readyState !== WebSocket.CLOSED) {
        ws.close(); // Ensure .onclose is triggered
      }
      attemptReconnect();
    };

    ws.onerror = (error) => {
      console.error(`${label} WebSocket error:`, error);
      // WebSocket will attempt to close after an error
    };

    ws.onmessage = (msg) => {
      const data = JSON.parse(msg.data);
      const transcript = data.channel?.alternatives[0]?.transcript;
      const isFinal = data.is_final ?? false;
      if (label === 'mic' && transcript !== '')
        console.log(data);
      if (transcript) {
        onTranscript({ user: label, transcription: transcript, isFinal });
      }
    };

    return ws;
  }, [label, onTranscript]);

  const attemptReconnect = useCallback(() => {
    if (isReconnectingRef.current || reconnectAttemptsRef.current >= MAX_RECONNECT_ATTEMPTS) {
      return;
    }

    isReconnectingRef.current = true;
    reconnectAttemptsRef.current += 1;
    
    console.log(`Attempting to reconnect ${label} WebSocket (attempt ${reconnectAttemptsRef.current}/${MAX_RECONNECT_ATTEMPTS})...`);
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    reconnectTimeoutRef.current = setTimeout(() => {
      if (streamRef.current) {
        try {
          if (!audioCtxRef.current || audioCtxRef.current.state === 'closed') {
            const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
            audioCtxRef.current = new AudioContextClass({ sampleRate: 16000 });
          }

          const workletNode = new AudioWorkletNode(audioCtxRef.current, 'pcm-processor');
          workletNodeRef.current = workletNode;

          if (sourceRef.current) {
            sourceRef.current.connect(workletNode);
          }

          setupWebSocket(workletNode);
        } catch (error) {
          console.error(`Failed to reconnect ${label} WebSocket:`, error);
          attemptReconnect(); // try again
        }
      }
    }, RECONNECT_DELAY);
  }, [label, setupWebSocket]);

  const start = async (stream?: MediaStream) => {
    try {
      // Clean up any existing connections first
      stop();
      
      reconnectAttemptsRef.current = 0;
      isReconnectingRef.current = false;

      const AudioContextClass = window.AudioContext || (window as any).webkitAudioContext;
      const audioContext = new AudioContextClass({ sampleRate: 16000 });
      audioCtxRef.current = audioContext;

      const processorCode = `
        class PCMProcessor extends AudioWorkletProcessor {
          process(inputs) {
            const input = inputs[0][0];
            if (!input) return true;
            const pcm = new Int16Array(input.length);
            for (let i = 0; i < input.length; i++) {
              const s = Math.max(-1, Math.min(1, input[i]));
              pcm[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
            }
            this.port.postMessage(pcm.buffer, [pcm.buffer]);
            return true;
          }
        }
        registerProcessor('pcm-processor', PCMProcessor);
      `;

      const blob = new Blob([processorCode], { type: 'application/javascript' });
      const processorUrl = URL.createObjectURL(blob);
      await audioContext.audioWorklet.addModule(processorUrl);

      const micStream = stream || (await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
        },
      }));
      
      streamRef.current = micStream;
      const source = audioContext.createMediaStreamSource(micStream);
      sourceRef.current = source;
      
      const workletNode = new AudioWorkletNode(audioContext, 'pcm-processor');
      workletNodeRef.current = workletNode;
      
      // Setup WebSocket
      setupWebSocket(workletNode);
      
      source.connect(workletNode);
    } catch (error) {
      console.error(`Error starting ${label} stream:`, error);
      throw error;
    }
  };

  const stop = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    
    if (wsRef.current) {
      wsRef.current.onclose = null; // Prevent reconnection attempts during intentional close
      wsRef.current.close();
      wsRef.current = null;
    }
    
    if (workletNodeRef.current) {
      workletNodeRef.current.port.onmessage = null;
      workletNodeRef.current.disconnect();
      workletNodeRef.current = null;
    }
    
    if (sourceRef.current) {
      sourceRef.current.disconnect();
      sourceRef.current = null;
    }
    
    if (audioCtxRef.current) {
      audioCtxRef.current.close();
      audioCtxRef.current = null;
    }
    
    streamRef.current = null;
    isReconnectingRef.current = false;
    reconnectAttemptsRef.current = 0;
  };

  return { start, stop };
};

