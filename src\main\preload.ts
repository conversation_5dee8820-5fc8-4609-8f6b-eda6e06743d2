// Disable no-unused-vars, broken for spread args
/* eslint no-unused-vars: off */
import { contextBridge, ipcRenderer, IpcRendererEvent, app } from 'electron';
import path from 'path';

export type Channels = 'ipc-example';

const electronHandler = {
  ipcRenderer: {
    sendMessage(channel: Channels, ...args: unknown[]) {
      ipcRenderer.send(channel, ...args);
    },
    on(channel: Channels, func: (...args: unknown[]) => void) {
      const subscription = (_event: IpcRendererEvent, ...args: unknown[]) =>
        func(...args);
      ipcRenderer.on(channel, subscription);

      return () => {
        ipcRenderer.removeListener(channel, subscription);
      };
    },
    once(channel: Channels, func: (...args: unknown[]) => void) {
      ipcRenderer.once(channel, (_event, ...args) => func(...args));
    }
  },
  getScreenSources: async () => {
    return await ipcRenderer.invoke('get-screen-sources');
  },
  getIsDebug: async () => {
    return await ipcRenderer.invoke('get-is-debug');
  },
  requestScreenPermission: async () => {
    return await ipcRenderer.invoke('request-screen-permission');
  },
  extractPdfText: (url: string) => ipcRenderer.invoke('extract-pdf-text', url),
  chat: {
    sendMessage: (messages: any[]) =>
      ipcRenderer.invoke('chat-completion', messages),
    onChunk: (callback: (chunk: string) => void) => {
      const listener = (_event: IpcRendererEvent, chunk: string) => callback(chunk);
      ipcRenderer.on('chat-chunk', listener);
      return () => ipcRenderer.removeListener('chat-chunk', listener);
    }
  },
  generateEmbedding: (text: string) => ipcRenderer.invoke('generate-embedding', text),
  minimizeApp: () => ipcRenderer.invoke('minimize-app'),
  closeApp: () => ipcRenderer.invoke('close-app'),
  getSummary: (transcriptions: string) => ipcRenderer.invoke('get-summary', transcriptions),
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  captureScreenshot: (sourceId: string) => 
    ipcRenderer.invoke('capture-screenshot', sourceId),
  autoUpdate: {
    checkForUpdates: () => ipcRenderer.invoke('check-for-updates'),
    installUpdate: () => ipcRenderer.send('install-update'),
    onUpdateStatus: (callback: (status: any) => void) => {
      const listener = (_event: IpcRendererEvent, status: any) => callback(status);
      ipcRenderer.on('update-status', listener);
      return () => ipcRenderer.removeListener('update-status', listener);
    },
    downloadUpdate: () => ipcRenderer.invoke('download-update'),
  }

};

contextBridge.exposeInMainWorld('electron', electronHandler);

export type ElectronHandler = typeof electronHandler;




