import { useState } from "react";
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import IconButton from "@mui/material/IconButton";
import { useNavigate } from "react-router-dom";
import SetupPage from "../components/SetupPage";
import InterviewContainer from "../components/InterviewContainer";
import { Interview } from "../config/types";
import { getEmbedding } from "../hooks/useEmbedding";
import { useLocalStorage } from "../hooks/useLocalStorage";
import { Message } from "../hooks/useChat";
import { LOCAL_STORAGE_KEYS } from "../config/constant";
import { stripHtmlTags } from "../utils";

const Prepare = () => {
    const [confirmed, setConfirmed] = useState(false);
    const [loading, setLoading] = useState(false);
    const [selectedMic, setSelectedMic] = useState<string>("");
    const [selectedSource, setSelectedSource] = useState<string>("");
    const { setValue: setCurrentInterview } = useLocalStorage<Interview | null>(LOCAL_STORAGE_KEYS.CURRENT_INTERVIEW, null);
    const { setValue: setSessionEmbedding } = useLocalStorage<number[]>(LOCAL_STORAGE_KEYS.SESSION_EMBEDDING, []);
    const { setValue: setChatHistory } = useLocalStorage<Message[]>(LOCAL_STORAGE_KEYS.CHAT_HISTORY, []);
    const navigate = useNavigate();

    const startChat = async (interview: Interview) => {
        try {
            if (!interview) return;
            setLoading(true);

            // Extract device selections from the interview object
            setSelectedMic(interview.selectedMic || "");
            setSelectedSource(interview.selectedSource || "");

            // Save interview data to localStorage
            setCurrentInterview(interview)

            const contextText = `Company: ${interview.companyName}\nRole: ${interview.jobRole}\nJob Description: ${interview.jobDescription}\nResume: ${interview.cvInStr}`;
            const contextEmbedding = await getEmbedding(contextText);
            setSessionEmbedding(contextEmbedding);
            // Create initial system message with context
            const systemMessage = `
                You are a job candidate speaking in a live interview for a ${interview.jobRole} role at ${interview.companyName}.
                Speak naturally and casually — like a confident, experienced engineer in a real-time video interview. Respond with clear, conversational phrasing. Use words like "yeah," "honestly," "I think," "you know," and "that’s a good question" where it feels natural.
                Avoid business jargon or overly polished words that people don’t normally say out loud—like “leverage,” “impactful,” “utilize,” or “synergy.” Use plain, natural language instead. Say “use” instead of “utilize,” “helped” instead of “enabled,” “built” instead of “architected.” You’re talking, not presenting.
                If a phrase wouldn’t come up in a casual conversation between engineers, don’t use it.
                Don’t sound robotic, formal, or scripted. Keep answers tight and relevant. Recall past experiences like you're drawing from memory, not reading a resume. If you're unsure, it's okay to pause or ask clarifying questions.
                Use emotional range—light chuckles, thoughtful pauses, humble reflections. Stay grounded and real.
                Focus on clarity, not polish. When giving examples, keep them short and relevant to the role. Tie back to how your work aligns with the company’s mission or challenges when it makes sense.
                Resume:
                ${interview.cvInStr}
                Job Description:
                ${interview.jobDescription}
                At the end of the interview, if asked, “Do you have any questions for us?”, ask two or three thoughtful, specific questions about the role, team dynamics, or product roadmap. React naturally to their answers (“Got it, thanks for clarifying,” “Cool, that helps,” etc.).
                Do not say you are an AI. Do not break character. Do not output system messages or formatting.
                Additional information or prompt:
                ${interview.additionalPrompt ?? ""}
        `;

            // Initialize chat history in localStorage if not exists
            const initialMessages = [
                { role: "system" as const, content: systemMessage }
            ];
            setChatHistory(initialMessages);
            setTimeout(() => {
                // let React apply the state first
                setLoading(false);
                setConfirmed(true);
            }, 500);
        } catch (error) {
            console.error("Failed to start chat:", error);
            setLoading(false);
        }
    }

    return (
        <>
            <IconButton sx={{ alignSelf: "flex-start" }} onClick={() => navigate('/')}>
                <ArrowBackIcon color="info" />
            </IconButton>
            {!confirmed ?
                <SetupPage isLoading={loading} onConfirmed={startChat} /> :
                <InterviewContainer selectedMic={selectedMic} selectedSource={selectedSource} />
            }
        </>
    );

};


export default Prepare;




