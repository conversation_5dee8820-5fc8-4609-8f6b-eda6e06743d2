import { MemoryRouter as Router, Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import './App.css';
import { JSX, useEffect, useState } from 'react';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Prepare from './pages/Prepare';
import { Box, IconButton, ThemeProvider, Typography } from '@mui/material';
import darkTheme from './theme';
import ClearIcon from '@mui/icons-material/Clear';
import LogoutIcon from '@mui/icons-material/Logout';
import RemoveIcon from '@mui/icons-material/Remove';
import Icon from '../../assets/mockmate_icon_256.png';
import { useLocalStorage } from './hooks/useLocalStorage';
import { LOCAL_STORAGE_KEYS } from './config/constant';
import { AuthProvider, useAuth } from './hooks/useAuth';
import CheckUpdates from './components/CheckUpdates';
const ProtectedRoute = ({ children }: { children: JSX.Element }) => {
  const { value: token } = useLocalStorage<string>(LOCAL_STORAGE_KEYS.AUTH_TOKEN, '');
  if (!token) {
    return <Navigate to="/login" />;
  }
  return children;
};

const ActionBar = () => {
  const navigate = useNavigate();
  const { username, setUsername, setToken } = useAuth();
  const minimizeApp = () => {
    window.electron.minimizeApp();
  };
  const closeApp = () => {
    window.electron.closeApp();
  };

  const signOut = () => {
    setToken('');
    setUsername('');
    setTimeout(() => {
      navigate('/login');
    }, 500);
  };

  return (
    <Box display="flex" justifyContent="flex-end" gap="2px">
      {username &&
        <Typography color="text.secondary" sx={{ alignSelf: 'center' }}>
          Signed as {username}
        </Typography>
      }
      {username && <IconButton
        onClick={signOut}
        size="small"
        title='Sign out'
        sx={{
          width: 36,
          height: 36,
          borderRadius: 0,
          backgroundColor: 'transparent',
          color: '#cfd8dc',
          '&:hover': {
            backgroundColor: '#2c2c2c',
          },
        }}
      >
        <LogoutIcon fontSize="small" />
      </IconButton>}
      <IconButton
        onClick={minimizeApp}
        size="small"
        title='Minimize'
        sx={{
          width: 36,
          height: 36,
          borderRadius: 0,
          backgroundColor: 'transparent',
          color: '#90caf9',
          '&:hover': {
            backgroundColor: '#1e1e1e',
          },
        }}
      >
        <RemoveIcon fontSize="small" />
      </IconButton>
      <IconButton
        onClick={closeApp}
        size="small"
        title='Close'
        sx={{
          width: 36,
          height: 36,
          borderRadius: 0,
          backgroundColor: 'transparent',
          color: '#ef9a9a',
          '&:hover': {
            backgroundColor: '#c62828',
            color: '#fff',
          },
        }}
      >
        <ClearIcon fontSize="small" />
      </IconButton>
    </Box>
  )
}

export default function App() {
  const [appVersion, setAppVersion] = useState('0.1.0');
  const [isDebug, setIsDebug] = useState(false);

  // Get the app version from package.json
  useEffect(() => {
    // This will work if you've exposed this through the preload script
    if (window.electron && window.electron.getAppVersion) {
      window.electron.getAppVersion().then((version: string) => {
        setAppVersion(version);
      });
    }
    if (window.electron && window.electron.getIsDebug) {
      window.electron.getIsDebug().then((isDebug: boolean) => {
        setIsDebug(isDebug);
      });
    }
  }, []);
  return (
    <ThemeProvider theme={darkTheme}>
      <AuthProvider>
        <Router>
          <Box display='flex' flexDirection='row' justifyContent='space-between'>
            <Box display='flex' flexDirection='row' alignItems='center' gap={1} justifyContent='flex-start'>
              <img style={{ width: '30px', height: '30px' }} src={Icon} alt='Mockmate' />
              <Typography>Mockmate v{appVersion} {isDebug ? "(Dev Mode)" : ""}</Typography>
              <CheckUpdates />
            </Box>
            <ActionBar />
          </Box>
          <Routes>
            <Route path="/login" element={<Login />} />
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/interview"
              element={
                <ProtectedRoute>
                  <Prepare />
                </ProtectedRoute>
              }
            />
          </Routes>
        </Router>
      </AuthProvider>
    </ThemeProvider>

  );
}

