/*
 * @NOTE: Prepend a `~` to css file paths that are in your node_modules
 *        See https://github.com/webpack-contrib/sass-loader#imports
 */
body {
  position: relative;
  color: white;
  height: 100vh;
  font-family: sans-serif;
  overflow-y: hidden;
  overflow-x: hidden;
  margin: 0;
}
#root {
  height: 100vh;
}
body, html, #root {
  background: rgba(0, 0, 0, 0.2); /* translucent white */
  backdrop-filter: blur(30px);          /* ✨ actual blur */
  -webkit-backdrop-filter: blur(30px);
  border-radius: 12px;
  overflow: hidden;
  clip-path: inset(0 round 12px);
}
div#root {
  -webkit-app-region: drag;
  cursor: grab;
}
div.MuiPaper-root,
div.MuiFormControl-root,
div.MuiInputBase-root,
div.no-drag {
  -webkit-app-region: no-drag;
  cursor: default;
}
button {
  background-color: white;
  padding: 10px 20px;
  border-radius: 10px;
  border: none;
  appearance: none;
  font-size: 1.3rem;
  box-shadow: 0px 8px 28px -6px rgba(24, 39, 75, 0.12),
    0px 18px 88px -4px rgba(24, 39, 75, 0.14);
  cursor: pointer;
  -webkit-app-region: no-drag;
}

::-webkit-scrollbar {
    width: 16px;
    height: 16px;
    background: transparent;
    height: 8px;
    width: 8px;
}

::-webkit-scrollbar-thumb, .mDPmMe::-webkit-scrollbar-thumb {
    border-bottom: 4px solid grey;
    border-left: 4px solid grey;
    border-right: 4px solid grey;
    border-top: 4px solid grey;
}

::-webkit-scrollbar-thumb {
    border: none;
    box-shadow: none;
    background: grey;
    border-radius: 8px;
    min-height: 40px;
}

::-webkit-scrollbar-corner {
  /* background: #1e1e1e; match your app's background */
  background: transparent;
}


/* Add this in your global CSS or a .module.css imported into InterviewContainer */
.split {
  height: 100%;
  width: 100%;
}

.custom-gutter {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  z-index: 10;
}

.custom-gutter .gutter-icon {
  width: 20px;
  height: 20px;
  pointer-events: none; /* Important: allows drag through the icon */
}
