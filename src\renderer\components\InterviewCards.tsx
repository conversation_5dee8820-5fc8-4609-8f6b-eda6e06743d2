import { FC } from "react";
import {
    <PERSON>,
    CardContent,
    Typo<PERSON>,
    <PERSON>,
    Box,
    Button,
} from "@mui/material";
import Grid from '@mui/material/Grid';
import { STEP_TYPE } from "../config/constant";
import { Interview } from "../config/types";

interface InterviewCardProps {
    interviews: Interview[];
    onRowClick: (interview: Interview) => void;
}

const InterviewCardView: FC<InterviewCardProps> = ({ interviews, onRowClick }) => {
    const upcomingSteps = interviews
        .map((interview) => {
            const pendingStep = interview.steps.find((step) => step.status === 0);
            if (pendingStep) {
                return {
                    _id: interview._id,
                    interviewId: interview._id,
                    projectName: interview.projectName,
                    companyName: interview.projectName,
                    jobRole: interview.jobRole,
                    accountId: interview.accountId,
                    jobDescription: interview.jobDescription,
                    step: pendingStep,
                    steps: [],
                };
            }
            return null;
        })
        .filter((item) => item !== null);

    return (
        <Grid
            container
            spacing={2}
            sx={{
                overflowY: "auto",
                paddingRight: 1,
                maxHeight: '600px',
                margin: 0,
            }}
        >
            {upcomingSteps.length > 0 ? (
                upcomingSteps.map(
                    (item) =>
                        item && (
                            <Grid size={{ xs: 12, sm: 6, md: 4 }} key={item.interviewId}>
                                <Card
                                    sx={{
                                        backgroundColor: "rgba(255, 255, 255, 0.08)",
                                        color: "#ffffff",
                                        height: "100%",
                                        borderRadius: 2,
                                        transition: "all 0.3s ease !important",
                                        "&:hover": {
                                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                                        },
                                    }}
                                >
                                    <CardContent>
                                        <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                                            <Typography
                                                variant="subtitle1"
                                                component="div"
                                                gutterBottom
                                                sx={{ fontWeight: "bold", fontSize: "16px" }}
                                            >
                                                {item.projectName}
                                            </Typography>
                                            <Typography variant="caption">{item.step.step}{" "}({STEP_TYPE[item.step.stepType]})</Typography>
                                        </Box>
                                        <Typography
                                            variant="body2"
                                            sx={{ mb: 0.5 }}
                                        >{item.jobRole}</Typography>

                                        <Typography
                                            variant="caption"
                                            color="text.secondary"
                                            display="block"
                                            sx={{ mb: 1 }}
                                        >
                                            Applied As: {item.accountId}
                                        </Typography>
                                        <Button
                                            variant="outlined"
                                            onClick={() => onRowClick(item)}
                                            size="small"
                                        >
                                            Start Interview
                                        </Button>
                                    </CardContent>
                                </Card>
                            </Grid>
                        )
                )
            ) : (
                <Grid size={12}>
                    <Box
                        sx={{
                            height: "100%",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            color: "#ccc",
                            fontStyle: "italic",
                            fontSize: "14px",
                        }}
                    >
                        No upcoming interviews found
                    </Box>
                </Grid>
            )}
        </Grid>
    );
};

export default InterviewCardView;
