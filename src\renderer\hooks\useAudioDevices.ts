// hooks/useAudioDevices.ts
import { useEffect, useState } from 'react';

export const useAudioDevices = () => {
  const [micDevices, setMicDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedMic, setSelectedMic] = useState<string>('');

  useEffect(() => {
    navigator.mediaDevices.enumerateDevices().then((devices) => {
      const mics = devices.filter((d) => d.kind === 'audioinput');
      setMicDevices(mics);
      if (mics.length > 0) setSelectedMic(mics[0].deviceId);
    });
  }, []);

  return { micDevices, selectedMic, setSelectedMic };
};
