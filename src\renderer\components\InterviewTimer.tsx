import React, { useState, useEffect } from 'react';
import { Typography } from '@mui/material';

interface InterviewTimerProps {
  isRunning: boolean;
}

const InterviewTimer: React.FC<InterviewTimerProps> = ({ isRunning }) => {
  const [elapsedTime, setElapsedTime] = useState(0);
  
  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;
    
    if (isRunning) {
      intervalId = setInterval(() => {
        setElapsedTime(prev => prev + 1);
      }, 1000);
    } else if (intervalId) {
      clearInterval(intervalId);
    }
    
    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [isRunning]);
  
  // Format seconds to MM:SS or HH:MM:SS
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  return (
    <Typography 
      variant="body2" 
      color="primary.light" 
      component="span"
      sx={{ ml: 1, fontFamily: 'monospace' }}
    >
      {formatTime(elapsedTime)}
    </Typography>
  );
};

export default InterviewTimer;