import { useRef } from 'react';
import { config } from '../config';

interface TranscriptEntry {
  user: 'mic' | 'system';
  transcription: string;
  isFinal: boolean;
}

export const useDeepgramStream = (
  label: 'mic' | 'system',
  onTranscript: (entry: TranscriptEntry) => void
) => {
  const wsRef = useRef<WebSocket | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);

  const DEEPGRAM_API_KEY = config.deepgram_api_key;

  const start = async (stream: MediaStream) => {
    const url = 'wss://api.deepgram.com/v1/listen?model=nova-2&language=en-US&punctuate=true&smart_format=true&vad_turnoff=500';
    const ws = new WebSocket(url, ['token', DEEPGRAM_API_KEY]);
    wsRef.current = ws;

    ws.onopen = () => {
      mediaRecorderRef.current = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      mediaRecorderRef.current.ondataavailable = (e) => {
        if (e.data.size > 0 && ws.readyState === WebSocket.OPEN) {
          ws.send(e.data);
        }
      };

      mediaRecorderRef.current.start(50);
    };
    ws.onerror = (err) => console.error('WebSocket error:', err);
    ws.onclose = () => console.log('WebSocket closed');
    ws.onmessage = (msg) => {
      const data = JSON.parse(msg.data);
      const transcript = data.channel?.alternatives[0]?.transcript;
      const isFinal = data.is_final ?? data.speech_final ?? false;

      if (transcript) {
        onTranscript({
          user: label,
          transcription: transcript,
          isFinal: false // Always set to false to allow appending
        });
      }
    };
  };

  const stop = () => {
    mediaRecorderRef.current?.stop();
    wsRef.current?.close();
  };

  return { start, stop };
};
