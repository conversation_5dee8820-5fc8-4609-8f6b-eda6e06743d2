type ScreenSource = {
    id: string;
    name: string;
    thumbnail: string;
  };
  
export const useSystemAudio = () => {
  const getSystemAudioStream = async () => {
    const stream = await (navigator.mediaDevices as any).getUserMedia({
      audio: {
          mandatory: {
            chromeMediaSource: 'desktop',
            chromeMediaSourceId: 'screen:0:0',
          },
        },
        video: {
          mandatory: {
            chromeMediaSource: 'desktop',
            chromeMediaSourceId: 'screen:0:0',
          },
        },
    });
    const audioOnlyStream = new MediaStream(stream.getAudioTracks());
    return audioOnlyStream;
  }
    return { getSystemAudioStream };
};