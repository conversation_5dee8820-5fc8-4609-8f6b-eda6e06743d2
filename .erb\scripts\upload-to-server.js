// scripts/upload-to-server.js
const fs = require('fs');
const path = require('path');

const SERVER_UPLOAD_URL = 'http://*************:8109/upload';
const SOURCE_DIR = path.resolve(__dirname, '../../release/build');

const filesToUpload = fs.readdirSync(SOURCE_DIR).filter(f =>
  /\.(exe|yml|blockmap)$/i.test(f)
);

if (filesToUpload.length === 0) {
  console.error('❌ No files found to upload.');
  process.exit(1);
}

const form = new FormData();
for (const file of filesToUpload) {
  const filePath = path.join(SOURCE_DIR, file);
  form.append('files', new Blob([fs.readFileSync(filePath)]), file);
}

fetch(SERVER_UPLOAD_URL, {
  method: 'POST',
  body: form,
})
  .then(res => {
    if (!res.ok) throw new Error(`Upload failed: ${res.statusText}`);
    return res.text();
  })
  .then(text => {
    console.log(`✅ Upload successful: ${text}`);
  })
  .catch(err => {
    console.error(`❌ Upload failed:`, err);
    process.exit(1);
  });
