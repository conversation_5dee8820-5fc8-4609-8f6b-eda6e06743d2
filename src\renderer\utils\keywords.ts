function extractKeywordsFromResume(resumeText: string): string[] {
  const keywords = new Set<string>();

  // Heuristics for common resume terms
  const knownTechnologies = [
    "React", "Next.js", "Node.js", "Electron", "TypeScript", "Deepgram",
    "AWS", "Azure", "MongoDB", "PostgreSQL", "Streamlit", "SQLite",
    "FastAPI", "Python", "Golang", "Java", "Rust", "Swift",
    "Kubernetes", "Docker", "Terraform", "Ansible", "Jenkins", "Git", "GitHub",
    "Bitbucket", "Jira", "Confluence", "Scrum", "Kanban", "Agile", "Waterfall",
    "Machine Learning", "AI", "Artificial Intelligence", "Natural Language Processing", "Mochi Health", "Zest AI"
  ];

  for (const tech of knownTechnologies) {
    const regex = new RegExp(`\\b${tech.replace('.', '\\.')}\\b`, 'i');
    if (regex.test(resumeText)) {
      keywords.add(tech);
    }
  }

  // Capture proper nouns (name, companies)
  const properNounRegex = /\b([A-Z][a-z]+(?:\s[A-Z][a-z]+)+)\b/g;
  let match;
  while ((match = properNounRegex.exec(resumeText)) !== null) {
    const phrase = match[1].trim();
    if (phrase.split(/\s+/).length <= 4) { // skip long sentences
      keywords.add(phrase);
    }
  }
  console.log(Array.from(keywords).slice(0, 50));
  return Array.from(keywords).slice(0, 50); // Deepgram limit: 50 keywords
}

export { extractKeywordsFromResume };
